#!/usr/bin/env python3
"""
测试修复后的买卖逻辑
验证多次买卖循环中的问题是否得到解决
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from datetime import datetime, timedelta
from strategy_engine_enhanced import Position

def test_profit_level_reset_fix():
    """测试止盈级别重置修复"""
    print("=== 测试止盈级别重置修复 ===")
    
    position = Position()
    
    # 场景1: 小幅加仓不重置
    print("场景1: 小幅加仓（<30%）不重置止盈级别")
    position.add_position(10000, 10.00, datetime.now())
    print(f"1. 买入10000股, 止盈级别: {position.profit_level_reached}")
    
    # 触发第一次止盈
    should_sell, level, ratio, reason = position.check_partial_sell(10.03)
    if should_sell:
        print(f"2. 触发止盈: {reason}, 新级别: {position.profit_level_reached}")
    
    # 小幅加仓（20% < 30%）
    position.add_position(2000, 9.95, datetime.now())
    print(f"3. 小幅加仓2000股(20%), 止盈级别: {position.profit_level_reached}")
    
    print("\n场景2: 大幅加仓（≥30%）自动重置")
    position.reset_partial_sold_flags()
    position.profit_level_reached = 2  # 模拟已达到第二级
    print(f"4. 模拟已达到止盈级别: {position.profit_level_reached}")
    
    # 大幅加仓（50% ≥ 30%）
    old_qty = position.total_quantity
    position.add_position(6000, 9.90, datetime.now())
    new_ratio = 6000 / position.total_quantity
    print(f"5. 大幅加仓6000股({new_ratio:.1%}), 止盈级别: {position.profit_level_reached}")

def test_improved_buy_conditions():
    """测试改进的买入条件"""
    print("\n=== 测试改进的买入条件 ===")
    
    from strategy_engine_enhanced import EnhancedStrategy
    
    strategy = EnhancedStrategy("159740", 1000000)
    
    # 模拟不同盈亏状态下的买入判断
    test_cases = [
        (10.00, 10.01, "小幅盈利1%"),
        (10.00, 10.25, "大幅盈利2.5%"),
        (10.00, 9.99, "小幅亏损0.1%"),
        (10.00, 9.95, "中等亏损0.5%"),
        (10.00, 9.90, "较大亏损1%"),
    ]
    
    for buy_price, current_price, description in test_cases:
        # 重置仓位
        strategy.position = Position()
        strategy.position.add_position(10000, buy_price, datetime.now())
        
        profit_rate = strategy.position.get_profit_rate(current_price)
        should_buy = strategy.should_buy(-0.007, current_price)  # 假设信号满足
        
        print(f"{description}: 收益率{profit_rate:.2%}, 是否允许买入: {should_buy}")

def test_batch_cleanup():
    """测试批次清理功能"""
    print("\n=== 测试批次清理功能 ===")
    
    position = Position()
    
    # 多次买入创建多个批次
    buys = [(3000, 10.00), (3000, 9.95), (4000, 9.90), (2000, 9.85)]
    for qty, price in buys:
        position.add_position(qty, price, datetime.now())
    
    print(f"买入后批次数量: {len(position.batches)}")
    print(f"总仓位: {position.total_quantity}股")
    
    # 部分卖出，应该清理空批次
    position.reduce_position(3000)  # 卖出第一个批次
    print(f"卖出3000股后批次数量: {len(position.batches)}")
    
    position.reduce_position(5000)  # 卖出第二个批次和部分第三个批次
    print(f"再卖出5000股后批次数量: {len(position.batches)}")
    print(f"剩余仓位: {position.total_quantity}股")

def test_comprehensive_scenario():
    """综合场景测试"""
    print("\n=== 综合场景测试 ===")
    
    position = Position()
    
    print("完整的买卖循环测试:")
    
    # 第一轮：买入 -> 分批止盈
    print("\n第一轮交易:")
    position.add_position(10000, 10.00, datetime.now())
    print(f"1. 买入10000股@10.00, 止盈级别: {position.profit_level_reached}")
    
    # 第一次止盈
    should_sell, level, ratio, reason = position.check_partial_sell(10.03)
    if should_sell:
        sell_qty = int(position.total_quantity * ratio)
        position.reduce_position(sell_qty)
        print(f"2. {reason}, 卖出{sell_qty}股, 剩余{position.total_quantity}股, 级别: {position.profit_level_reached}")
    
    # 第二轮：价格下跌后大幅加仓
    print("\n第二轮交易:")
    old_level = position.profit_level_reached
    position.add_position(8000, 9.85, datetime.now())  # 大幅加仓
    print(f"3. 价格下跌加仓8000股@9.85")
    print(f"   加仓前止盈级别: {old_level}, 加仓后: {position.profit_level_reached}")
    
    current_profit = position.get_profit_rate(9.90)
    print(f"   当前收益率: {current_profit:.2%}")
    
    # 检查是否能正常止盈
    should_sell, level, ratio, reason = position.check_partial_sell(9.95)
    if should_sell:
        print(f"4. 价格回升后触发: {reason}, 卖出比例: {ratio:.1%}")
    else:
        print(f"4. 价格回升到9.95，暂未触发止盈")

def compare_before_after():
    """对比修复前后的行为"""
    print("\n=== 修复前后对比 ===")
    
    print("修复前的问题:")
    print("1. 分批止盈后再买入，止盈级别不重置，导致跳级")
    print("2. 盈利时完全禁止买入，阻止合理加仓")
    print("3. 批次数量不断增加，内存占用上升")
    
    print("\n修复后的改进:")
    print("1. 大幅加仓(≥30%)时自动重置止盈级别")
    print("2. 只有大幅盈利(>2%)时才禁止买入")
    print("3. 自动清理空批次，优化内存使用")
    print("4. 更智能的买入条件判断")

if __name__ == "__main__":
    test_profit_level_reset_fix()
    test_improved_buy_conditions()
    test_batch_cleanup()
    test_comprehensive_scenario()
    compare_before_after()
