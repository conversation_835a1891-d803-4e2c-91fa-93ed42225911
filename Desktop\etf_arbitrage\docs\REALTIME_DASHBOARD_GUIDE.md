# 🚀 增强版实时交易模拟仪表板 - 使用指南

## 🎯 功能概述

这是一个基于增强版策略的实时交易模拟系统，支持自定义持仓情况，让您可以在真实的市场数据环境中测试和优化交易策略。

## 🚀 快速启动

### 1. 启动仪表板
```bash
# 使用启动脚本（推荐）
python run_realtime_dashboard.py

# 或直接启动streamlit
streamlit run app_enhanced_realtime_dashboard.py --server.port 8502
```

### 2. 访问界面
- 浏览器自动打开：`http://localhost:8502`
- 端口8502避免与回测面板冲突

## 📊 界面功能详解

### 左侧配置面板

#### 🎛️ 交易配置
- **交易标的**: 选择要模拟的ETF
- **初始资金**: 设置模拟交易的初始资金

#### 📊 策略参数
- **买入触发跌幅**: 价格下跌多少时触发买入
- **止损线**: 最大亏损容忍度
- **最大持仓时间**: 超时强制卖出

#### 💼 初始持仓（核心功能）
- **初始持仓数量**: 设置开始时的持仓数量
- **初始持仓成本**: 设置持仓的平均成本
- 支持从任意持仓状态开始模拟

#### 📈 显示设置
- **图表时间窗口**: 控制图表显示的时间范围
- **自动刷新**: 开启实时数据更新
- **刷新间隔**: 设置数据更新频率

#### 🎮 交易控制
- **🚀 启动**: 开始实时交易模拟
- **⏹️ 停止**: 完全停止模拟
- **⏸️ 暂停**: 暂停策略执行
- **▶️ 继续**: 恢复策略执行
- **🔄 重置**: 清空所有数据重新开始

### 主界面显示

#### 📊 实时状态卡片
1. **当前价格**: 最新tick价格
2. **当前持仓**: 持仓数量和市值
3. **浮动盈亏**: 实时盈亏和收益率
4. **交易信号**: 当前信号强度

#### 📈 实时交易监控图表
- **上图**: 价格走势 + 买卖点标记
- **中图**: 持仓数量变化
- **下图**: 交易信号强度（红色=买入信号，绿色=卖出信号）

#### 💰 净值变化图表
- 实时净值曲线
- 初始资金基准线
- 盈亏变化趋势

#### 📋 交易记录表
- 显示最近10笔交易
- 包含时间、动作、数量、价格、原因等

#### 🎯 策略状态信息
- **持仓信息**: 数量、成本、持仓时间
- **风险管理**: 净值、损失、回撤等

## 🎯 使用场景

### 1. 策略验证
- 在真实数据环境中测试策略逻辑
- 观察策略在不同市场条件下的表现
- 验证风险控制机制的有效性

### 2. 持仓管理
- 模拟从现有持仓开始的交易
- 测试不同持仓成本下的策略表现
- 优化持仓规模和成本控制

### 3. 参数调优
- 实时调整策略参数
- 观察参数变化对交易的影响
- 找到最优的参数组合

### 4. 风险评估
- 监控实时风险指标
- 测试极端市场条件下的表现
- 验证止损和风控机制

## 💡 使用技巧

### 参数设置建议
1. **新手用户**: 
   - 买入触发: -0.004
   - 止损: -0.015
   - 持仓时间: 1800秒

2. **进阶用户**:
   - 买入触发: -0.006
   - 止损: -0.020
   - 持仓时间: 3600秒

3. **激进策略**:
   - 买入触发: -0.008
   - 止损: -0.025
   - 持仓时间: 7200秒

### 持仓设置技巧
- **空仓开始**: 数量=0，测试完整策略流程
- **已有持仓**: 设置实际持仓数量和成本，模拟继续交易
- **成本测试**: 设置不同成本价，测试盈亏敏感性

### 监控要点
1. **信号质量**: 观察信号强度分布，避免过于频繁交易
2. **持仓时间**: 监控平均持仓时间，优化时间参数
3. **盈亏比例**: 关注单笔交易盈亏分布
4. **风险控制**: 确保止损机制正常工作

## ⚠️ 注意事项

### 数据要求
- 确保数据库中有实时或近期的tick数据
- 建议在交易时间内使用以获得最佳效果
- 数据延迟可能影响模拟的真实性

### 性能考虑
- 自动刷新会消耗系统资源
- 建议根据需要调整刷新间隔
- 长时间运行可能积累大量历史数据

### 模拟限制
- 这是模拟交易，不涉及真实资金
- 实际交易中可能存在滑点、延迟等因素
- 建议结合回测结果综合评估策略

## 🔧 故障排除

### 常见问题
1. **无数据显示**: 检查数据库连接和数据可用性
2. **策略不执行**: 确认已点击"启动"按钮
3. **图表不更新**: 检查自动刷新设置
4. **持仓设置无效**: 确保在启动前设置持仓参数

### 性能优化
- 适当调整图表时间窗口
- 合理设置刷新间隔
- 定期重置清理历史数据

---

🎉 **开始您的实时交易模拟之旅！**