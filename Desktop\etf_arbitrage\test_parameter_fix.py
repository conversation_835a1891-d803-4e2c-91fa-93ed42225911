#!/usr/bin/env python3
"""
测试参数传递修复
验证回测程序是否正确使用面板设置的参数
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from datetime import datetime
from strategy_engine_enhanced import Position
from backtest_enhanced import BacktestConfig

def test_default_config():
    """测试默认配置"""
    print("=== 测试默认配置 ===")
    
    position = Position()
    position.add_position(10000, 10.00, datetime.now())
    
    # 使用默认配置
    should_sell, level, ratio, reason = position.check_partial_sell(10.03)
    print(f"默认配置 - 价格10.03, 收益率: {position.get_profit_rate(10.03):.4f}")
    print(f"是否卖出: {should_sell}, 级别: {level}, 比例: {ratio:.1%}, 原因: {reason}")
    
    # 检查默认配置的止盈阈值
    from strategy_config import _DEFAULT_CONFIG
    print(f"\n默认配置止盈阈值:")
    print(f"第一次: {_DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier1']:.4f}")
    print(f"第二次: {_DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier2']:.4f}")
    print(f"第三次: {_DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier3']:.4f}")

def test_custom_config():
    """测试自定义配置"""
    print("\n=== 测试自定义配置 ===")
    
    # 创建自定义配置（模拟用户在面板上的设置）
    config = BacktestConfig(
        profit_target=0.0025,  # 0.25%
        partial_profit_multiplier1=1.0,  # 1倍
        partial_profit_multiplier2=1.0,  # 1倍
        partial_profit_multiplier3=2.0,  # 2倍
        partial_sell_ratio1=0.3,  # 30%
        partial_sell_ratio2=0.4,  # 40%
        partial_sell_ratio3=0.3   # 30%
    )
    
    position = Position()
    position.add_position(10000, 10.00, datetime.now())
    
    # 使用自定义配置
    should_sell, level, ratio, reason = position.check_partial_sell(10.03, config)
    print(f"自定义配置 - 价格10.03, 收益率: {position.get_profit_rate(10.03):.4f}")
    print(f"是否卖出: {should_sell}, 级别: {level}, 比例: {ratio:.1%}, 原因: {reason}")
    
    print(f"\n自定义配置止盈阈值:")
    print(f"第一次: {config.profit_target * config.partial_profit_multiplier1:.4f}")
    print(f"第二次: {config.profit_target * config.partial_profit_multiplier2:.4f}")
    print(f"第三次: {config.profit_target * config.partial_profit_multiplier3:.4f}")

def test_threshold_comparison():
    """测试阈值对比"""
    print("\n=== 阈值对比测试 ===")
    
    # 默认配置
    from strategy_config import _DEFAULT_CONFIG
    default_threshold1 = _DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier1']
    
    # 自定义配置
    custom_threshold1 = 0.0025 * 1.0  # 0.25%
    
    print(f"默认第一次止盈阈值: {default_threshold1:.4f} ({default_threshold1:.2%})")
    print(f"自定义第一次止盈阈值: {custom_threshold1:.4f} ({custom_threshold1:.2%})")
    print(f"差异: {abs(default_threshold1 - custom_threshold1):.4f}")
    
    # 测试实际收益率
    test_price = 10.03
    cost_price = 10.00
    actual_profit_rate = (test_price - cost_price) / cost_price
    print(f"\n实际收益率: {actual_profit_rate:.4f} ({actual_profit_rate:.2%})")
    print(f"是否超过默认阈值: {actual_profit_rate >= default_threshold1}")
    print(f"是否超过自定义阈值: {actual_profit_rate >= custom_threshold1}")

def test_csv_data_analysis():
    """分析CSV数据中的问题"""
    print("\n=== CSV数据分析 ===")
    
    # 模拟CSV中的数据
    # 第一次止盈: 0.0043 (0.43%)
    csv_profit_rate = 0.0043
    
    # 默认配置阈值
    from strategy_config import _DEFAULT_CONFIG
    default_threshold1 = _DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier1']
    
    # 用户期望的阈值
    expected_threshold1 = 0.0025 * 1.0  # 0.25%
    
    print(f"CSV显示的收益率: {csv_profit_rate:.4f} ({csv_profit_rate:.2%})")
    print(f"默认配置阈值: {default_threshold1:.4f} ({default_threshold1:.2%})")
    print(f"用户期望阈值: {expected_threshold1:.4f} ({expected_threshold1:.2%})")
    
    print(f"\n分析结果:")
    print(f"CSV收益率 > 默认阈值: {csv_profit_rate > default_threshold1}")
    print(f"CSV收益率 > 期望阈值: {csv_profit_rate > expected_threshold1}")
    print(f"这解释了为什么显示的收益率高于期望值")

if __name__ == "__main__":
    test_default_config()
    test_custom_config()
    test_threshold_comparison()
    test_csv_data_analysis()
