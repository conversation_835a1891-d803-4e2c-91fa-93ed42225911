#!/usr/bin/env python3
"""
测试止损修复
"""

from backtest_enhanced import BacktestConfig, EnhancedBacktest
from strategy_config import StrategyConfig
import logging

def test_stop_loss_fix():
    """测试止损修复"""
    
    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
    
    print("=== 测试止损配置修复 ===")
    
    # 测试原始配置
    print("\n1. 原始配置 (backtest_enhanced.py):")
    config1 = BacktestConfig()
    print(f"   stop_loss: {config1.stop_loss} ({config1.stop_loss:.2%})")
    
    # 测试策略配置
    print("\n2. 策略配置 (strategy_config.py):")
    strategy_stop_loss = StrategyConfig.get_param_config('stop_loss').default_value
    print(f"   原始值: {strategy_stop_loss} ({strategy_stop_loss:.2%})")
    print(f"   修复后应该是: {-strategy_stop_loss} ({-strategy_stop_loss:.2%})")
    
    # 模拟dashboard的配置处理
    print("\n3. Dashboard配置处理:")
    dashboard_stop_loss = -StrategyConfig.get_param_config('stop_loss').default_value
    print(f"   dashboard_stop_loss: {dashboard_stop_loss} ({dashboard_stop_loss:.2%})")
    
    # 创建使用dashboard配置的回测配置
    config2 = BacktestConfig(stop_loss=dashboard_stop_loss)
    print(f"   最终配置: {config2.stop_loss} ({config2.stop_loss:.2%})")
    
    # 测试止损逻辑
    print("\n4. 测试止损逻辑:")
    backtest = EnhancedBacktest(config2)
    
    # 模拟仓位
    backtest.position.quantity = 1000000
    backtest.position.avg_cost = 4.5380
    backtest.position.total_cost = 4538000
    
    from datetime import datetime, timedelta
    backtest.position.first_buy_time = datetime.now() - timedelta(seconds=60)
    
    # 测试不同的亏损情况
    test_losses = [-0.01, -0.02, -0.03, -0.05, -0.06]
    
    for loss in test_losses:
        current_price = 4.5380 * (1 + loss)
        should_sell, sell_ratio, reason = backtest.should_sell(current_price, datetime.now())
        
        expected_trigger = loss <= config2.stop_loss
        status = "✅" if should_sell == expected_trigger else "❌"
        
        print(f"   亏损 {loss:.2%}: 卖出={should_sell}, 预期={expected_trigger} {status}")
        if should_sell:
            print(f"      原因: {reason}")

if __name__ == "__main__":
    test_stop_loss_fix()