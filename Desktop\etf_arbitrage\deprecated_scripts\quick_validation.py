#!/usr/bin/env python3
"""
快速验证参数统一化修复
"""

def quick_validation():
    print("=== 快速验证参数统一化 ===")
    
    # 1. 验证统一配置
    try:
        from strategy_config import StrategyConfig
        defaults = StrategyConfig.get_default_values()
        
        print(f"✅ 统一配置系统正常")
        print(f"   参数数量: {len(defaults)}")
        
        # 检查关键参数的符号
        key_params = ['buy_trigger_drop', 'stop_loss', 'profit_target']
        for param in key_params:
            value = defaults[param]
            if param in ['buy_trigger_drop', 'stop_loss']:
                status = "✅" if value < 0 else "❌"
                print(f"   {param}: {value} (负值) {status}")
            else:
                status = "✅" if value > 0 else "❌"
                print(f"   {param}: {value} (正值) {status}")
                
    except Exception as e:
        print(f"❌ 统一配置错误: {e}")
        return False
    
    # 2. 验证策略引擎
    try:
        from strategy_engine_enhanced import BUY_TRIGGER_DROP, STOP_LOSS, PROFIT_TARGET
        print(f"✅ 策略引擎导入正常")
        print(f"   BUY_TRIGGER_DROP: {BUY_TRIGGER_DROP}")
        print(f"   STOP_LOSS: {STOP_LOSS}")
        print(f"   PROFIT_TARGET: {PROFIT_TARGET}")
        
    except Exception as e:
        print(f"❌ 策略引擎错误: {e}")
        return False
    
    # 3. 验证回测配置
    try:
        from backtest_enhanced import BacktestConfig
        config = BacktestConfig()
        print(f"✅ 回测配置正常")
        print(f"   stop_loss: {config.stop_loss}")
        print(f"   profit_target: {config.profit_target}")
        
    except Exception as e:
        print(f"❌ 回测配置错误: {e}")
        return False
    
    print(f"\n🎉 参数统一化验证通过！")
    return True

if __name__ == "__main__":
    quick_validation()