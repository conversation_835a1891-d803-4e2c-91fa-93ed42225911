import sqlite3
import time
import logging
from typing import Optional, <PERSON>ple
from datetime import datetime, timedelta

import numpy as np
import pandas as pd

DB_PATH: str = "ticks.db"

# 策略参数
BUY_TRIGGER_DROP: float = -0.006          # 20秒回报率 <= -0.6% 触发买
PROFIT_TARGET: float = 0.006              # 持仓收益率达到0.6%时一次性卖出
LAYERS: Tuple[float, float, float] = (0.4, 0.35, 0.25)  # 分层买入占比
MAX_POSITION: int = 10000                  # 最大持仓

logger = logging.getLogger(__name__)


def _init_logger(level: int = logging.INFO) -> None:
    logging.basicConfig(level=level, format="%(asctime)s %(levelname)s %(message)s")


def _safe_return_20s(df: pd.DataFrame) -> float:
    """
    计算近20秒收益率 r=(p1-p0)/p0；异常与空数据返回0.0
    """
    try:
        if df is None or df.shape[0] < 2:
            return 0.0
        p0 = float(df["price"].iloc[0])
        p1 = float(df["price"].iloc[-1])
        if p0 == 0.0:
            return 0.0
        r = (p1 - p0) / p0
        if np.isnan(r) or np.isinf(r):
            return 0.0
        return float(r)
    except Exception as e:
        logger.error(f"计算20秒收益率失败: {e}")
        return 0.0


def _load_recent_ticks(conn: sqlite3.Connection, symbol: str, span_seconds: int = 20) -> pd.DataFrame:
    """
    读取近 span_seconds 秒的逐笔
    """
    try:
        since_iso = (datetime.now() - timedelta(seconds=span_seconds)).isoformat(timespec="seconds")
        df = pd.read_sql_query(
            "SELECT tick_time AS time, price, volume FROM ticks "
            "WHERE symbol=? AND tick_time>=? ORDER BY tick_time ASC",
            conn,
            params=[symbol, since_iso],
            parse_dates=["time"]
        )
        if df.empty:
            return df
        df["price"] = pd.to_numeric(df["price"], errors="coerce")
        df["volume"] = pd.to_numeric(df["volume"], errors="coerce").fillna(0).astype(int)
        return df.dropna(subset=["price"])
    except Exception as e:
        logger.error(f"读取近窗口ticks失败: {e}")
        return pd.DataFrame(columns=["time", "price", "volume"])


def _ensure_signal_table(conn: sqlite3.Connection) -> None:
    try:
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute(
            """CREATE TABLE IF NOT EXISTS strategy_signals(
                symbol TEXT NOT NULL,
                ts TEXT NOT NULL,
                signal TEXT NOT NULL,
                PRIMARY KEY(symbol, ts)
            )"""
        )
        conn.commit()
    except Exception as e:
        logger.error(f"初始化signal表失败: {e}")


def _upsert_signal(conn: sqlite3.Connection, symbol: str, ts: datetime, sig: str) -> None:
    """
    写入策略信号(B/S)，ts 精确到秒，主键(symbol, ts)避免重复
    """
    try:
        conn.execute(
            "INSERT OR REPLACE INTO strategy_signals(symbol, ts, signal) VALUES (?, ?, ?)",
            (symbol, ts.isoformat(timespec="seconds"), sig.upper())
        )
        conn.commit()
    except Exception as e:
        logger.error(f"写入策略信号失败: {e}")


def run_strategy_loop(symbol: str, poll_sec: float = 1.0) -> None:
    """
    实时循环：
    - 读取近20秒tick，计算收益率
    - 若 r<=BUY_TRIGGER_DROP 且 仓位<MAX_POSITION：按 LAYERS 叠加买入，记录一次 B 信号
    - 若持仓收益率>=PROFIT_TARGET 且 仓位>0：一次性清仓，记录一次 S 信号
    """
    try:
        conn = sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES)
    except Exception as e:
        logger.error(f"打开数据库失败: {e}")
        return

    _ensure_signal_table(conn)

    # 跟踪仓位和持仓成本
    position: int = 0
    total_cost: float = 0.0  # 总成本
    avg_cost: float = 0.0    # 平均成本

    while True:
        try:
            df = _load_recent_ticks(conn, symbol, span_seconds=20)
            r = _safe_return_20s(df)
            
            # 获取当前价格
            current_price = float(df["price"].iloc[-1]) if not df.empty else 0.0

            # 若有tick则以最后一笔时间戳作为信号时间；否则用当前时间
            ts = df["time"].iloc[-1].to_pydatetime() if not df.empty else datetime.now()

            # 买入触发：按分层叠加，写入一次B信号
            if r <= BUY_TRIGGER_DROP and position < MAX_POSITION and current_price > 0:
                remaining = MAX_POSITION - position
                bought = 0
                for pct in LAYERS:
                    qty = int(MAX_POSITION * pct)
                    if qty <= 0 or remaining <= 0:
                        continue
                    alloc = min(qty, remaining)
                    
                    # 更新持仓成本
                    total_cost += alloc * current_price
                    position += alloc
                    bought += alloc
                    remaining -= alloc
                
                # 更新平均成本
                if position > 0:
                    avg_cost = total_cost / position
                
                if bought > 0:
                    _upsert_signal(conn, symbol, ts, "B")
                    logger.info(f"买入信号: 仓位={position}, 平均成本={avg_cost:.4f}, 当前价格={current_price:.4f}")

                # 限制频率
                time.sleep(1.0 if poll_sec is None or poll_sec < 1.0 else poll_sec)
                continue  # 下一轮

            # 卖出触发：当持仓收益率达到目标时一次性清仓
            if position > 0 and avg_cost > 0 and current_price > 0:
                profit_rate = (current_price - avg_cost) / avg_cost
                if profit_rate >= PROFIT_TARGET:
                    position = 0
                    total_cost = 0.0
                    avg_cost = 0.0
                    _upsert_signal(conn, symbol, ts, "S")
                    logger.info(f"卖出信号: 收益率={profit_rate:.4f}, 目标={PROFIT_TARGET:.4f}, 当前价格={current_price:.4f}")
                    time.sleep(0.5 if poll_sec is None or poll_sec < 0.5 else poll_sec)
                    continue

            # 常规休眠
            time.sleep(poll_sec if (poll_sec is not None and poll_sec > 0) else 1.0)

        except Exception as e:
            logger.error(f"策略循环异常: {e}")
            time.sleep(1.0)


if __name__ == "__main__":
    import argparse
    _init_logger()
    p = argparse.ArgumentParser()
    p.add_argument("--symbol", required=True, type=str)
    p.add_argument("--poll-sec", type=float, default=1.0)
    args = p.parse_args()
    run_strategy_loop(args.symbol, args.poll_sec)