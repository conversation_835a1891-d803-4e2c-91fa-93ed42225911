# 实时交易监控面板更新说明

## 🎯 完成的功能

### 📈 实时交易监控图表（三图布局）

#### **上图**: 价格走势 + 买卖点标记
- ✅ 实时价格分时线图
- ✅ 策略买卖信号标记（绿色向上三角形=买入，红色向下三角形=卖出）
- ✅ 最新价格水平线提示
- ✅ 价格轴动态缩放，便于观察细微波动

#### **中图**: 持仓数量变化
- ✅ 实时持仓数量曲线
- ✅ 零轴基准线
- ✅ 填充区域显示持仓变化趋势
- ✅ 支持从数据库加载持仓数据或从交易记录计算

#### **下图**: 交易信号强度
- ✅ 红色柱状图 = 买入信号强度 (+1.0)
- ✅ 绿色柱状图 = 卖出信号强度 (-1.0)
- ✅ 实时显示策略信号触发情况

### 💰 净值变化图表
- ✅ **实时净值曲线**: 蓝色线条 + 填充区域
- ✅ **初始资金基准线**: 橙色虚线标记
- ✅ **盈亏变化趋势**: 柱状图显示相对初始资金的盈亏
  - 绿色柱 = 盈利
  - 红色柱 = 亏损
- ✅ 双Y轴设计：左轴显示净值，右轴显示盈亏

## 🔧 技术实现

### 新增数据加载函数
```python
def load_positions_today(symbol: str) -> pd.DataFrame
def load_equity_today(symbol: str) -> pd.DataFrame
```

### 图表构建函数更新
```python
def build_price_figure(df, latest_price, positions_df, signals_df) -> go.Figure
def build_equity_figure(equity_df, initial_capital=1000000.0) -> go.Figure
```

### 界面布局优化
- 🎨 美化头部：渐变色背景 + 图标
- 📊 双行布局：上行为监控图表+买卖五档，下行为净值图表
- 🎯 卡片式设计：圆角边框 + 阴影效果
- 📱 响应式布局：自适应不同屏幕尺寸

## 📊 数据源支持

### 持仓数据
- **优先**: `positions` 表 (ts, position, pnl)
- **备选**: 从 `ticks` 表的交易记录计算累计持仓

### 净值数据
- **数据源**: `equity` 表 (ts, equity, pnl)
- **默认**: 初始资金 1,000,000 元

### 交易信号
- **数据源**: `strategy_signals` 表 (ts, signal)
- **支持**: B/S 信号标准化处理

## 🚀 启动方式

```bash
# 启动实时监控面板
python app_dashboard.py --symbol 159740 --host 127.0.0.1 --port 8055

# 带调试信息启动
python app_dashboard.py --symbol 159740 --port 8055 --debug
```

## 🌐 访问地址

- **本地访问**: http://127.0.0.1:8055/
- **局域网访问**: http://[本机IP]:8055/

## 📋 功能特性

### 实时更新
- ⏱️ 2秒刷新间隔
- 🔄 自动数据同步
- 📡 Playwright监听东方财富推送

### 交互功能
- 🔍 图表缩放和平移
- 📊 多子图联动
- 💡 悬停提示信息

### 时间轴优化
- 📅 自动隐藏周末
- 🕐 午休时间断档 (11:30-13:00)
- ⏰ 智能时间范围调整

## 🎨 视觉设计

### 配色方案
- **价格线**: 蓝色 (#1f77b4)
- **买入信号**: 绿色 (#2ca02c)
- **卖出信号**: 红色 (#d62728)
- **持仓线**: 橙色 (#ff7f0e)
- **净值线**: 蓝色 (#1f77b4)

### 图表样式
- **模板**: plotly_white
- **字体**: 系统默认
- **图例**: 水平布局，顶部显示
- **边距**: 优化显示空间

## ✅ 测试验证

所有功能已通过语法检查和基本功能测试：
- ✅ 语法检查通过
- ✅ 应用启动成功
- ✅ 图表渲染正常
- ✅ 数据加载兼容

## 📝 使用说明

1. **启动应用**: 运行上述启动命令
2. **访问界面**: 浏览器打开对应地址
3. **查看数据**: 实时监控价格、持仓、信号、净值变化
4. **交互操作**: 可缩放、平移图表进行详细分析

---

🎉 **实时交易监控面板已成功升级，包含完整的三图监控和净值分析功能！**