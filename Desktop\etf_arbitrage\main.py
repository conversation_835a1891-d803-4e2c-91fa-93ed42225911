#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一入口：交互式菜单
- 初始化数据库
- 采集逐笔（API轮询 或 Playwright 监听）
- 回测（含指标/报表/图表）
- 实时模拟

运行：python main.py
"""
"""
主要功能

●初始化数据库：调用 init_db.py
●采集逐笔
    ○ API 轮询：fetch_ticks_eastmoney.api_poll_loop（推荐上手）
    ○ Playwright 监听：fetch_ticks_eastmoney.run（需安装 playwright && playwright install）
●回测：调用 backtest.backtest，并支持
    ○ 统计扩展：年化收益/波动、Sharpe/Sortino、Calmar、胜率、Profit Factor、持仓时间、曝险比例、最大回撤
    ○ 报表导出：trades.csv、rounds.csv、equity_resampled.csv（--report-dir）
    ○ 净值+回撤双图（--plot-file）
●实时模拟：调用 strategy_realtime.run

使用方法

●运行：python main.py

●菜单说明：
    ○ 1 初始化数据库
    ○ 2 采集逐笔（API 轮询）
    ○ 3 采集逐笔（Playwright 监听）
    ○ 4 回测（交互式设置参数、可输出报表与图）
    ○ 5 实时模拟
    ○ 0 退出

●如果选择 Playwright 监听而未安装依赖，按提示安装：
    ○ pip install playwright
    ○ playwright install

●备注
    ○ 回测导出的报表目录和图文件在菜单中可交互设置。
    ○ API 采集和回测均默认标的 159740，可在菜单中修改。
"""
import asyncio
import sys
import threading
import time
import csv
from pathlib import Path
from typing import List, Optional
import subprocess
import os

def _ask(prompt: str, default: Optional[str] = None) -> str:
    p = f"{prompt} [{default}]: " if default is not None else f"{prompt}: "
    try:
        s = input(p).strip()
        return s if s else (default if default is not None else "")
    except (EOFError, KeyboardInterrupt):
        print("\n用户中断，返回菜单。")
        return default if default is not None else ""

def _ask_int(prompt: str, default: int) -> int:
    s = _ask(prompt, str(default))
    try:
        return int(s)
    except Exception:
        print(f"输入无效，使用默认值 {default}")
        return int(default)

def _ask_float(prompt: str, default: float) -> float:
    s = _ask(prompt, str(default))
    try:
        return float(s)
    except Exception:
        print(f"输入无效，使用默认值 {default}")
        return float(default)

def _ask_list_float(prompt: str, default_list: List[float]) -> List[float]:
    default_str = ",".join(str(x) for x in default_list)
    s = _ask(prompt, default_str)
    try:
        vals = [float(x.strip()) for x in s.split(",") if x.strip() != ""]
        return vals if vals else list(default_list)
    except Exception:
        print(f"输入无效，使用默认值 {default_list}")
        return list(default_list)

def _ask_bool(prompt: str, default: bool) -> bool:
    s = _ask(prompt, "y" if default else "n").lower()
    if s in ("y","yes","1","t","true","是","好"):
        return True
    if s in ("n","no","0","f","false","否","不"):
        return False
    return default

def action_init_db() -> None:
    try:
        from init_db import init_db, DB  # type: ignore
    except Exception as e:
        print(f"导入初始化模块失败: {e}")
        return
    try:
        init_db()
        print(f"已初始化数据库: {DB}")
    except Exception as e:
        print(f"初始化数据库失败: {e}")

def action_fetch_api() -> None:
    symbol_input = _ask("标的代码，可逗号分隔(如: 159740,159934,510300)", "159740,159934,510300")
    symbols = [s.strip() for s in symbol_input.split(",") if s.strip()]
    poll_seconds = _ask_int("API 轮询间隔秒", 5)
    count = _ask_int("每次拉取最近条数(count)", 200)
    continuous = _ask_bool("在交易时间内持续监听? (y/n)", True)
    print("开始 API 轮询，Ctrl+C 可停止...")
    try:
        fmod = __import__("fetch_ticks_eastmoney")
        if not hasattr(fmod, "api_poll_loop"):
            print("fetch_ticks_eastmoney 中未找到 api_poll_loop")
            return
        threads: List[threading.Thread] = []
        for sym in symbols:
            t = threading.Thread(
                target=fmod.api_poll_loop,
                args=(sym,),
                kwargs={"poll_seconds": poll_seconds, "count": count, "continuous": continuous},
                daemon=True,
            )
            t.start()
            threads.append(t)
        print(f"已启动 {len(threads)} 个API轮询: {', '.join(symbols)}")
        try:
            while any(t.is_alive() for t in threads):
                time.sleep(0.5)
        except KeyboardInterrupt:
            print("\n已停止 API 轮询。")
    except Exception as e:
        print(f"启动 API 轮询失败: {e}\n提示：该脚本顶层依赖 Playwright，未安装可能导致导入失败。请执行: pip install playwright && playwright install")

def action_fetch_playwright() -> None:
    symbol_input = _ask("标的代码，可逗号分隔(如: 159740,159934,510300)", "159740,159934,510300")
    symbols = [s.strip() for s in symbol_input.split(",") if s.strip()]
    dump_dir = _ask("响应样本保存目录(可为空)", "")
    dump_dir = dump_dir if dump_dir else None
    listen_seconds = _ask_int("监听秒数", 30)
    continuous = _ask_bool("在交易时间内持续监听? (y/n)", True)
    print("启动 Playwright 监听，Ctrl+C 可停止...")
    try:
        fmod = __import__("fetch_ticks_eastmoney")
        if not hasattr(fmod, "run"):
            print("fetch_ticks_eastmoney 中未找到 run (async)")
            return
        async def _run_many():
            tasks = [asyncio.create_task(fmod.run(sym, dump_dir, listen_seconds, continuous=continuous)) for sym in symbols]
            await asyncio.gather(*tasks)
        asyncio.run(_run_many())
    except KeyboardInterrupt:
        print("\n已停止 Playwright 监听。")
    except Exception as e:
        print(f"启动 Playwright 失败: {e}\n请确保已安装: pip install playwright && playwright install")

def action_backtest() -> None:
    try:
        from backtest import (
            load_ticks,
            backtest as run_backtest,
            BUY_DROP,
            SELL_RISE,
            LAYER_QTY,
            BASE_QTY,
            DEFAULT_SLIPPAGE,
            DEFAULT_FEE,
        )
    except Exception as e:
        print(f"导入回测模块失败: {e}")
        return

    symbol_input = _ask("标的代码，可逗号分隔(如: 159740,510300)", "159740")
    symbols = [s.strip() for s in symbol_input.split(",") if s.strip()]
    buy_drop = _ask_float("买入触发跌幅(buy_drop)", BUY_DROP)
    sell_rise = _ask_float("卖出触发涨幅(sell_rise)", SELL_RISE)
    base_qty = _ask_int("基准下单股数(base_qty)", BASE_QTY)
    layers = _ask_list_float("分层比例(逗号分隔)", LAYER_QTY)
    slippage = _ask_float("基础滑点比例(slippage)", DEFAULT_SLIPPAGE)
    fee = _ask_float("手续费比例(fee)", DEFAULT_FEE)
    slp_coef = _ask_float("滑点体量系数(slippage_coef)", 0.0)
    init_cap = _ask_float("初始资金(initial_capital)", 1_000_000.0)
    rf = _ask_float("风险利率(年化, risk_free_rate)", 0.0)
    freq = _ask("重采样频率(sample_freq)", "1T")
    report_dir = _ask("报表目录(report_dir，可为空)", "")
    report_dir = report_dir if report_dir else None
    plot_file = _ask("净值图文件路径(plot_file，可为空)", "equity.png")
    plot_file = plot_file if plot_file else None

    def _plot_for(sym: str, pf: Optional[str]) -> Optional[str]:
        if not pf:
            return None
        p = Path(pf)
        # 若单标的则直接使用输入；多标的自动加后缀
        if len(symbols) <= 1:
            return str(p)
        return str(p.with_name(f"{p.stem}_{sym}{p.suffix}"))

    summary_rows: List[dict] = []
    try:
        for sym in symbols:
            df = load_ticks(sym)
            if df.empty:
                print(f"{sym} 无可用 tick 数据，跳过。")
                continue
            per_report = str(Path(report_dir) / sym) if report_dir else None
            per_plot = _plot_for(sym, plot_file)
            stats = run_backtest(
                df,
                buy_drop=buy_drop,
                sell_rise=sell_rise,
                base_qty=base_qty,
                layer_qty=layers,
                base_slippage=slippage,
                fee=fee,
                slippage_coef=slp_coef,
                plot_file=per_plot,
                initial_capital=init_cap,
                risk_free_rate=rf,
                sample_freq=freq,
                report_dir=per_report,
            )
            print(f"[{sym}] 回测完成，关键统计：")
            keys = [
                "final_equity","pnl","cum_return","annual_return","annual_vol",
                "sharpe_annual","sortino_annual","calmar","max_drawdown",
                "num_trades","num_rounds","win_rate","profit_factor",
                "avg_hold_time_secs","median_hold_time_secs","exposure_ratio",
            ]
            row = {"symbol": sym}
            for k in keys:
                v = stats.get(k)
                row[k] = v
                print(f"{k}: {v}")
            summary_rows.append(row)
            print(f"trades 数: {len(stats.get('trades', []))}")
            if per_plot:
                print(f"已输出净值/回撤图: {per_plot}")
            if per_report:
                print(f"已导出报表到目录: {per_report}")

        # 输出汇总
        if report_dir and summary_rows:
            try:
                out_dir = Path(report_dir)
                out_dir.mkdir(parents=True, exist_ok=True)
                out_file = out_dir / "summary.csv"
                fieldnames = ["symbol","final_equity","pnl","cum_return","annual_return","annual_vol","sharpe_annual","sortino_annual","calmar","max_drawdown","num_trades","num_rounds","win_rate","profit_factor","avg_hold_time_secs","median_hold_time_secs","exposure_ratio"]
                with out_file.open("w", newline="", encoding="utf-8") as f:
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    for r in summary_rows:
                        writer.writerow(r)
                print(f"已导出汇总: {out_file}")
            except Exception as e:
                print(f"汇总导出失败: {e}")

        if not summary_rows:
            print("没有可汇总的结果。")
    except Exception as e:
        print(f"回测执行失败: {e}")

def action_realtime() -> None:
    try:
        from strategy_realtime import run as realtime_run  # type: ignore
    except Exception as e:
        print(f"导入实时策略失败: {e}")
        return
    symbol_input = _ask("标的代码，可逗号分隔(如: 159740,510300)", "159740")
    symbols = [s.strip() for s in symbol_input.split(",") if s.strip()]
    print("启动实时模拟，自动启动tick采集(API轮询，持续监听)，Ctrl+C 可停止...")
    try:
        # 自动启动 tick 采集（API轮询，持续监听）
        fmod_ticks = __import__("fetch_ticks_eastmoney")
        ingest_threads: List[threading.Thread] = []
        for sym in symbols:
            t_ingest = threading.Thread(
                target=fmod_ticks.api_poll_loop,
                args=(sym,),
                kwargs={"poll_seconds": 2, "count": 200, "continuous": True},
                daemon=True,
            )
            t_ingest.start()
            ingest_threads.append(t_ingest)

        # 启动实时策略线程
        threads: List[threading.Thread] = []
        for sym in symbols:
            t = threading.Thread(target=realtime_run, args=(sym,), daemon=True)
            t.start()
            threads.append(t)
        print(f"已启动 tick 采集与实时模拟: {', '.join(symbols)}")
        try:
            while any(t.is_alive() for t in threads):
                time.sleep(0.5)
        except KeyboardInterrupt:
            print("\n已停止实时模拟。")
    except Exception as e:
        print(f"实时模拟失败: {e}")


def action_dashboard() -> None:
    try:
        from app_dashboard import run_dashboard  # type: ignore
    except Exception as e:
        print(f"导入监测面板失败: {e}")
        return
    symbol = _ask("监测标的(单一，如: 159740)", "159740")
    host = _ask("服务Host", "127.0.0.1")
    port = _ask_int("服务端口", 8050)
    print("启动监测面板中... Ctrl+C 可停止")
    try:
        run_dashboard(symbol=symbol, host=host, port=port)
    except KeyboardInterrupt:
        print("\n已停止监测面板。")
    except Exception as e:
        print("监测面板启动失败: {0}\n请先安装: pip install dash feffery-antd-components pandas plotly requests".format(e))

def action_backtest_panel() -> None:
    """
    启动回测监测面板（Dash+Feffery）
    """
    symbol = _ask("回测标的(单一，如: 159740)", "159740")
    host = _ask("服务Host", "127.0.0.1")
    port = _ask_int("服务端口", 8051)

    python = sys.executable
    p = None
    try:
        print("启动回测监测面板中... Ctrl+C 可停止")
        p = subprocess.Popen([python, "app_backtest_dashboard.py", "--symbol", symbol, "--host", host, "--port", str(port)])
        print(f"已启动：回测面板 → http://{host}:{port}")
        p.wait()
    except KeyboardInterrupt:
        print("\n停止回测面板...")
    except Exception as e:
        print(f"回测面板启动失败: {e}")
    finally:
        try:
            if p and p.poll() is None:
                p.terminate()
        except Exception:
            pass

def action_oneclick() -> None:
    """
    一键运行：采集(API连续)+策略引擎+监测面板。Ctrl+C 统一退出。
    """
    symbol = _ask("一键运行标的(单一，如: 159740)", "159740")
    host = "127.0.0.1"
    port = _ask_int("面板端口", 8050)

    python = sys.executable
    procs: List = []
    try:
        print("启动采集(API连续)...")
        p1 = subprocess.Popen([python, "fetch_ticks_eastmoney.py", "--symbol", symbol, "--continuous"])
        procs.append(p1)

        print("启动策略引擎...")
        p2 = subprocess.Popen([python, "strategy_engine.py", "--symbol", symbol, "--poll-sec", "1"])
        procs.append(p2)

        print("启动监测面板...")
        p3 = subprocess.Popen([python, "app_dashboard.py", "--symbol", symbol, "--host", host, "--port", str(port)])
        procs.append(p3)

        print(f"已启动：采集+策略+面板 → http://{host}:{port}  (Ctrl+C 停止)")
        while True:
            time.sleep(1.0)
    except KeyboardInterrupt:
        print("\n收到中断，正在停止子进程...")
    except Exception as e:
        print(f"一键运行启动失败: {e}")
    finally:
        for p in procs:
            try:
                p.terminate()
            except Exception:
                pass
        time.sleep(0.5)
        for p in procs:
            try:
                if p.poll() is None:
                    p.kill()
            except Exception:
                pass
        print("已停止所有子进程。")

def main() -> None:
    while True:
        print("\n==== ETF Arbitrage 统一入口 ====")
        print("1) 初始化数据库")
        print("2) 采集逐笔 - API轮询")
        print("3) 采集逐笔 - Playwright监听")
        print("4) 回测")
        print("5) 实时模拟")
        print("6) 实时监测面板(Dash+Feffery)")
        print("7) 一键运行：采集+策略+面板")
        print("8) 回测监测面板(Dash+Feffery-回测)")
        print("0) 退出")
        choice = _ask("请选择功能编号", "4")
        if choice == "1":
            action_init_db()
        elif choice == "2":
            action_fetch_api()
        elif choice == "3":
            action_fetch_playwright()
        elif choice == "4":
            action_backtest()
        elif choice == "5":
            action_realtime()
        elif choice == "6":
            action_dashboard()
        elif choice == "7":
            action_oneclick()
        elif choice == "8":
            action_backtest_panel()
        elif choice == "0":
            print("已退出。")
            break
        else:
            print("无效的选择，请重试。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n已退出。")
        sys.exit(0)