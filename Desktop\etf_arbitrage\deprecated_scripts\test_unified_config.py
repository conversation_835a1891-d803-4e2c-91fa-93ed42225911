#!/usr/bin/env python3
"""
测试统一配置系统
"""

from strategy_config import StrategyConfig
import logging

def test_unified_config():
    """测试统一配置系统"""
    
    print("=== 测试统一配置系统 ===")
    
    # 测试获取所有参数
    print("\n1. 所有参数配置:")
    all_params = StrategyConfig.get_all_params()
    for name, config in all_params.items():
        print(f"   {name}: {config.default_value} ({config.description})")
    
    # 测试获取默认值
    print(f"\n2. 默认值字典:")
    defaults = StrategyConfig.get_default_values()
    for name, value in defaults.items():
        print(f"   {name}: {value}")
    
    # 测试预设配置
    print(f"\n3. 预设配置:")
    for preset_name in StrategyConfig.STRATEGY_PRESETS.keys():
        print(f"   {preset_name}:")
        preset_config = StrategyConfig.get_preset_config(preset_name)
        for key, value in preset_config.items():
            if key in ['buy_trigger_drop', 'stop_loss', 'profit_target']:
                print(f"     {key}: {value} ({value:.2%})")
    
    # 测试参数验证
    print(f"\n4. 参数验证测试:")
    test_config = {
        'stop_loss': -0.15,  # 超出范围
        'profit_target': 0.1,  # 超出范围
        'buy_trigger_drop': -0.005  # 正常范围
    }
    
    errors = StrategyConfig.validate_config(test_config)
    if errors:
        print("   发现错误:")
        for param, error in errors.items():
            print(f"     {param}: {error}")
    else:
        print("   配置验证通过")
    
    # 测试正负符号一致性
    print(f"\n5. 正负符号检查:")
    print(f"   buy_trigger_drop: {defaults['buy_trigger_drop']} (应为负值)")
    print(f"   stop_loss: {defaults['stop_loss']} (应为负值)")
    print(f"   profit_target: {defaults['profit_target']} (应为正值)")
    print(f"   daily_loss_limit: {defaults['daily_loss_limit']} (应为负值)")
    print(f"   max_drawdown_limit: {defaults['max_drawdown_limit']} (应为负值)")
    
    # 验证符号正确性
    checks = [
        ('buy_trigger_drop', defaults['buy_trigger_drop'] < 0),
        ('stop_loss', defaults['stop_loss'] < 0),
        ('profit_target', defaults['profit_target'] > 0),
        ('daily_loss_limit', defaults['daily_loss_limit'] < 0),
        ('max_drawdown_limit', defaults['max_drawdown_limit'] < 0)
    ]
    
    print(f"\n6. 符号正确性验证:")
    all_correct = True
    for param_name, is_correct in checks:
        status = "✅" if is_correct else "❌"
        print(f"   {param_name}: {status}")
        if not is_correct:
            all_correct = False
    
    if all_correct:
        print(f"\n🎉 统一配置系统测试通过！")
    else:
        print(f"\n❌ 统一配置系统存在问题，需要修复。")

if __name__ == "__main__":
    test_unified_config()