#!/usr/bin/env python3
"""
测试界面数据一致性
"""

import pandas as pd
from backtest_enhanced import run_enhanced_backtest, BacktestConfig

def test_interface_data():
    """测试界面数据一致性"""
    
    print("=== 测试界面数据一致性 ===")
    
    # 使用与界面相同的配置
    config = BacktestConfig(
        symbol='159740',
        start_date='2025-08-25',
        end_date='2025-09-01',
        initial_capital=1000000
    )
    
    print("运行回测...")
    results = run_enhanced_backtest(config)
    
    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return
    
    # 获取性能数据
    perf = results['performance']
    raw_data = results['raw_data']
    
    print(f"\n=== 界面显示的数据 ===")
    print(f"总收益率: {perf['总收益率']}")
    print(f"最大回撤: {perf['最大回撤']}")
    print(f"胜率: {perf['胜率']}")
    print(f"总交易次数: {perf['总交易次数']}")
    print(f"买入次数: {perf['买入次数']}")
    print(f"卖出次数: {perf['卖出次数']}")
    
    # 验证原始数据
    trades_df = raw_data['trades']
    print(f"\n=== 原始数据验证 ===")
    print(f"交易记录总数: {len(trades_df)}")
    
    if not trades_df.empty:
        buy_trades = trades_df[trades_df['type'] == 'BUY']
        sell_trades = trades_df[trades_df['type'] == 'SELL']
        
        print(f"买入交易数: {len(buy_trades)}")
        print(f"卖出交易数: {len(sell_trades)}")
        
        # 验证胜率计算
        if 'pnl' in sell_trades.columns:
            profitable = len(sell_trades[sell_trades['pnl'] > 0])
            losing = len(sell_trades[sell_trades['pnl'] < 0])
            breakeven = len(sell_trades[sell_trades['pnl'] == 0])
            
            print(f"盈利交易: {profitable}")
            print(f"亏损交易: {losing}")
            print(f"盈亏平衡: {breakeven}")
            
            if len(sell_trades) > 0:
                calculated_win_rate = profitable / len(sell_trades)
                print(f"计算胜率: {calculated_win_rate:.2%}")
                
                # 检查与界面显示是否一致
                displayed_win_rate = float(perf['胜率'].rstrip('%')) / 100
                print(f"界面胜率: {displayed_win_rate:.2%}")
                
                if abs(calculated_win_rate - displayed_win_rate) < 0.001:
                    print("✅ 胜率数据一致")
                else:
                    print("❌ 胜率数据不一致")
                    print(f"差异: {abs(calculated_win_rate - displayed_win_rate):.4f}")
        
        # 检查是否有异常的交易
        if 'pnl' in sell_trades.columns:
            # 检查是否所有交易都是盈利的
            if len(sell_trades[sell_trades['pnl'] <= 0]) == 0:
                print("⚠️ 警告: 所有卖出交易都是盈利的")
                print("这可能表明:")
                print("1. 策略参数过于保守")
                print("2. 止损逻辑未正确执行")
                print("3. 回测期间市场表现异常好")
            
            # 显示PnL分布
            pnl_stats = sell_trades['pnl'].describe()
            print(f"\nPnL统计:")
            print(f"最小值: {pnl_stats['min']:.2f}")
            print(f"最大值: {pnl_stats['max']:.2f}")
            print(f"平均值: {pnl_stats['mean']:.2f}")
            print(f"中位数: {pnl_stats['50%']:.2f}")
    
    # 检查净值曲线
    equity_df = raw_data['equity_curve']
    if not equity_df.empty:
        initial_equity = equity_df['equity'].iloc[0]
        final_equity = equity_df['equity'].iloc[-1]
        calculated_return = (final_equity - initial_equity) / initial_equity
        
        print(f"\n=== 净值验证 ===")
        print(f"初始净值: {initial_equity:,.2f}")
        print(f"最终净值: {final_equity:,.2f}")
        print(f"计算收益率: {calculated_return:.2%}")
        
        displayed_return = float(perf['总收益率'].rstrip('%')) / 100
        print(f"界面收益率: {displayed_return:.2%}")
        
        if abs(calculated_return - displayed_return) < 0.001:
            print("✅ 收益率数据一致")
        else:
            print("❌ 收益率数据不一致")
    
    # 模拟导出CSV
    timestamp = "2025-09-01T03-08"
    csv_filename = f"{timestamp}_export.csv"
    trades_df.to_csv(csv_filename, index=True)
    print(f"\n=== CSV导出测试 ===")
    print(f"已导出到: {csv_filename}")
    
    # 读取并验证CSV
    csv_df = pd.read_csv(csv_filename)
    csv_sell = csv_df[csv_df['type'] == 'SELL']
    
    if not csv_sell.empty and 'pnl' in csv_df.columns:
        csv_profitable = len(csv_sell[csv_sell['pnl'] > 0])
        csv_total = len(csv_sell)
        csv_win_rate = csv_profitable / csv_total if csv_total > 0 else 0
        
        print(f"CSV胜率: {csv_win_rate:.2%}")
        print(f"CSV交易数: {csv_total}")
        
        # 检查CSV是否被过滤
        if len(csv_df) != len(trades_df):
            print(f"⚠️ CSV数据被过滤: 原始{len(trades_df)}条 -> CSV{len(csv_df)}条")
        else:
            print("✅ CSV数据完整")

if __name__ == "__main__":
    test_interface_data()
