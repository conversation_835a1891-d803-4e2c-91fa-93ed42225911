#!/usr/bin/env python3
"""
增强版策略回测系统（修复版）
支持增强版策略的所有功能：风险控制、分批交易、动态参数等
"""

import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from typing import List, Optional, Tuple
from datetime import datetime
import logging

# 导入增强策略的组件
# 导入增强策略的组件
from strategy_engine_enhanced import Position, RiskManager, MarketRegime
# 导入统一配置
from strategy_config import StrategyConfig

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class BacktestConfig:
    """回测配置 - 使用统一参数配置"""
    
    def __init__(self, **kwargs):
        """初始化配置，使用统一配置的默认值"""
        # 基本参数
        self.symbol = kwargs.get('symbol', "159740")
        self.start_date = kwargs.get('start_date', "2025-08-25")
        self.end_date = kwargs.get('end_date', "2025-08-27")
        
        # 从统一配置获取默认值
        defaults = StrategyConfig.get_default_values()
        
        # 设置所有参数，优先使用传入的kwargs，否则使用默认值
        for key, default_value in defaults.items():
            setattr(self, key, kwargs.get(key, default_value))
    
    # 风险控制参数
    daily_loss_limit: float = -0.05    # 日损失限制
    max_drawdown_limit: float = -0.10  # 最大回撤限制
    exit_at_close: bool = False        # 是否在收盘前平仓


class EnhancedBacktest:
    """增强版回测引擎"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.position = Position()
        self.risk_manager = RiskManager(config.initial_capital)
        
        # 回测结果记录
        self.trades = []
        self.equity_curve = []
        self.signals = []
        
        # 性能统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_commission = 0.0
        
    def load_data(self) -> pd.DataFrame:
        """加载回测数据"""
        try:
            conn = sqlite3.connect("ticks.db")
            
            query = """
            SELECT tick_time as time, price, volume 
            FROM ticks 
            WHERE symbol=? AND tick_time BETWEEN ? AND ?
            ORDER BY tick_time ASC
            """
            
            df = pd.read_sql_query(
                query, conn, 
                params=[self.config.symbol, self.config.start_date, self.config.end_date],
                parse_dates=['time']
            )
            conn.close()
            
            if df.empty:
                raise ValueError(f"没有找到 {self.config.symbol} 在 {self.config.start_date} 到 {self.config.end_date} 的数据")
            
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0)
            df = df.dropna(subset=['price'])
            
            logger.info(f"加载数据: {len(df)} 条记录，时间范围: {df['time'].min()} 到 {df['time'].max()}")
            return df
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_signal(self, df: pd.DataFrame, idx: int) -> float:
        """计算交易信号（最高价回撤，与实时监控一致）"""
        try:
            window = self.config.signal_window
            if idx < window:
                return 0.0
            
            # 使用配置的窗口大小计算最高价回撤（与实时监控保持一致）
            window_data = df.iloc[max(0, idx-window):idx+1]
            
            if len(window_data) < 2:
                return 0.0
            
            high_price = float(window_data['price'].max())
            current_price = float(window_data['price'].iloc[-1])
            
            if high_price == 0:
                return 0.0
            
            # 计算从最高点的回撤（负值表示下跌）
            signal = (current_price - high_price) / high_price
            return signal
            
        except Exception as e:
            logger.error(f"计算信号失败: {e}")
            return 0.0
    
    def should_buy(self, signal: float, current_price: float) -> bool:
        """判断是否应该买入"""
        # 信号检查
        if signal <= self.config.buy_trigger_drop:
            # 仓位检查
            if self.position.total_quantity >= self.config.max_position:
                return False
            
            # 价格检查
            if current_price <= 0:
                return False
            
            # 风险控制检查
            if not self.risk_manager.check_risk_limits():
                return False
            
            return True
        
        return False
    
    def should_sell(self, current_price: float, current_time: datetime) -> Tuple[bool, float, str]:
        """判断卖出条件"""
        if self.position.total_quantity <= 0:
            return False, 0.0, ""
        
        profit_rate = self.position.get_profit_rate(current_price)
        
        # 计算持仓时间（传入当前回测时间）
        hold_time = self.position.get_hold_time(current_time)
        
        # 止损检查 - 使用动态参数，但添加最小持仓时间保护
        if profit_rate <= self.config.stop_loss and hold_time >= self.config.min_hold_time:
            return True, 1.0, f"止损: {profit_rate:.2%} (设置: {self.config.stop_loss:.2%})"
        
        # 时间止损
        if hold_time >= self.config.max_hold_time:
            return True, 1.0, f"时间止损: {hold_time}s"
        
        # 分批止盈检查 - 使用动态参数
        partial_sell_result = self.position.check_partial_sell(current_price, self.config)
        if partial_sell_result[0]:
            # 找到满足条件的分批止盈
            _, level_idx, sell_ratio, reason = partial_sell_result
            return True, sell_ratio, reason
        
        return False, 0.0, ""
    
    def execute_buy(self, current_price: float, current_time: datetime) -> int:
        """执行买入"""
        remaining = self.config.max_position - self.position.total_quantity
        total_bought = 0
        
        # 使用动态分层参数
        layers = [self.config.layer1_ratio, self.config.layer2_ratio, self.config.layer3_ratio]
        
        for pct in layers:
            # 使用position_size参数替代max_position计算每层买入数量
            qty = int(self.config.position_size * pct)
            if qty <= 0 or remaining <= 0:
                continue
            
            alloc = min(qty, remaining)
            
            # 考虑滑点
            actual_price = current_price * (1 + self.config.slippage)
            
            self.position.add_position(alloc, actual_price, current_time)
            total_bought += alloc
            remaining -= alloc
            
            # 计算手续费
            commission = alloc * actual_price * self.config.commission_rate
            self.total_commission += commission
        
        if total_bought > 0:
            self.trades.append({
                'time': current_time,
                'type': 'BUY',
                'quantity': total_bought,
                'price': current_price,
                'actual_price': actual_price,
                'reason': '分层买入'
            })
            
            logger.debug(f"买入: {total_bought}@{actual_price:.4f}, 总仓位: {self.position.total_quantity}")
        
        return total_bought
    
    def execute_sell(self, current_price: float, current_time: datetime, 
                    sell_ratio: float, reason: str, batch_size: Optional[int] = None) -> List[Tuple[int, float]]:
        """
        执行卖出（支持分批卖出）
        
        Args:
            current_price: 当前价格
            current_time: 当前时间
            sell_ratio: 卖出比例
            reason: 卖出原因
            batch_size: 单批次卖出数量，如果为None则使用配置的batch_size
            
        Returns:
            List[Tuple[int, float]]: 每笔交易的数量和价格
        """
        sell_qty = int(self.position.total_quantity * sell_ratio)
        if sell_qty <= 0:
            return []
        
        # 修复：合理设置batch_size，避免过度分批
        batch_size = batch_size or max(sell_qty, 1000)  # 至少1000股一批，或者一次性卖完
        
        # 如果卖出数量较小，直接一次性卖出
        if sell_qty <= 2000:
            batch_size = sell_qty
        
        num_batches = max(1, (sell_qty + batch_size - 1) // batch_size)
        executed_trades = []
        total_proceeds = 0
        total_cost_reduced = 0
        total_commission = 0
        
        for i in range(num_batches):
            this_batch_size = min(batch_size, sell_qty - sum(qty for qty, _ in executed_trades))
            if this_batch_size <= 0:
                break
                
            actual_price = current_price * (1 - self.config.slippage * (1 + i * 0.01))
            cost_reduced = self.position.reduce_position(this_batch_size)
            commission = this_batch_size * actual_price * self.config.commission_rate
            
            total_proceeds += this_batch_size * actual_price
            total_cost_reduced += cost_reduced
            total_commission += commission
            
            executed_trades.append((this_batch_size, actual_price))
        
        # 修复：只记录一次卖出交易，而不是每批都记录
        if executed_trades:
            total_pnl = total_proceeds - total_cost_reduced
            self.total_commission += total_commission

            # 更新胜率统计
            self.total_trades += 1
            if total_pnl > 0:
                self.winning_trades += 1

            # 只记录一次交易记录
            trade_record = {
                'time': current_time,
                'type': 'SELL',
                'quantity': sum(qty for qty, _ in executed_trades),
                'price': current_price,
                'actual_price': sum(qty * price for qty, price in executed_trades) / sum(qty for qty, _ in executed_trades),
                'pnl': total_pnl,
                'reason': f"{reason} ({num_batches}批次)" if num_batches > 1 else reason
            }
            self.trades.append(trade_record)
        
        return executed_trades
    
    def run_backtest(self) -> Dict:
        """运行回测"""
        logger.info("开始增强版策略回测...")
        
        # 加载数据
        df = self.load_data()
        if df.empty:
            return {'error': '无法加载数据'}
        
        # 回测主循环
        for idx in range(len(df)):
            row = df.iloc[idx]
            current_time = row['time']
            current_price = float(row['price'])
            
            # 计算信号
            signal = self.calculate_signal(df, idx)
            
            # 记录信号
            self.signals.append({
                'time': current_time,
                'price': current_price,
                'signal': signal,
                'position': self.position.total_quantity
            })
            
            # 更新净值 - 修复计算逻辑
            if self.position.total_quantity > 0:
                # 有持仓时：净值 = 现金 + 持仓市值
                market_value = self.position.total_quantity * current_price
                # 现金 = 初始资金 - 已投入成本 - 手续费 + 已实现盈亏
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                cash = self.config.initial_capital - self.position.total_cost - self.total_commission + realized_pnl
                current_equity = cash + market_value
            else:
                # 无持仓时：净值 = 初始资金 + 已实现盈亏 - 手续费
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                current_equity = self.config.initial_capital + realized_pnl - self.total_commission

            self.risk_manager.update_equity(current_equity)
            
            # 记录净值曲线
            if self.position.total_quantity > 0:
                market_value = self.position.total_quantity * current_price
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                cash = self.config.initial_capital - self.position.total_cost - self.total_commission + realized_pnl
            else:
                market_value = 0
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                cash = self.config.initial_capital + realized_pnl - self.total_commission

            self.equity_curve.append({
                'time': current_time,
                'equity': current_equity,
                'position': self.position.total_quantity,
                'price': current_price,
                'cash': cash,
                'market_value': market_value,
                'realized_pnl': realized_pnl,
                'total_commission': self.total_commission,
                'drawdown': (current_equity - self.risk_manager.peak_equity) / self.risk_manager.peak_equity if self.risk_manager.peak_equity > 0 else 0
            })
            
            # 检查是否需要收盘前平仓
            if (hasattr(self.config, 'exit_at_close') and self.config.exit_at_close and 
                self.position.total_quantity > 0):
                # 检查是否接近收盘时间（14:55以后）
                if (current_time.hour == 14 and current_time.minute >= 55) or current_time.hour == 15:
                    logger.info(f"收盘前平仓: {current_time}")
                    self.execute_sell(current_price, current_time, 1.0, "收盘前平仓")
                    continue
            
            # 卖出判断（优先级高于买入）
            should_sell, sell_ratio, sell_reason = self.should_sell(current_price, current_time)
            if should_sell:
                self.execute_sell(current_price, current_time, sell_ratio, sell_reason)
                # 卖出后跳过本tick的买入判断，避免同时买卖
                continue
            
            # 买入判断（只有在没有卖出时才执行）
            if self.should_buy(signal, current_price):
                # 买入前重置止盈标记，确保新买入的仓位可以享受完整的止盈策略
                if self.position.total_quantity > 0:
                    # 如果已有仓位，重置部分止盈标记
                    self.position.reset_partial_sold_flags()
                self.execute_buy(current_price, current_time)
        
        # 计算最终结果
        return self.calculate_results()
    
    def calculate_results(self) -> Dict:
        """计算回测结果"""
        if not self.equity_curve:
            return {'error': '没有回测数据'}
        
        equity_df = pd.DataFrame(self.equity_curve)
        
        # 基本统计
        initial_equity = self.config.initial_capital
        final_equity = equity_df['equity'].iloc[-1]
        total_return = (final_equity - initial_equity) / initial_equity
        
        # 最大回撤
        peak = equity_df['equity'].cummax()
        drawdown = (equity_df['equity'] - peak) / peak
        max_drawdown = drawdown.min()
        
        # 胜率 - 基于实际交易数据计算
        sell_trades = [t for t in self.trades if t['type'] == 'SELL' and 'pnl' in t]
        if sell_trades:
            profitable_trades = len([t for t in sell_trades if t['pnl'] > 0])
            win_rate = profitable_trades / len(sell_trades)
        else:
            win_rate = 0
        
        # 夏普比率（简化计算）
        returns = equity_df['equity'].pct_change().dropna()
        if len(returns) > 1 and returns.std() > 0:
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)  # 年化
        else:
            sharpe_ratio = 0
        
        # 交易统计
        buy_trades = [t for t in self.trades if t['type'] == 'BUY']
        sell_trades = [t for t in self.trades if t['type'] == 'SELL']
        
        results = {
            'config': self.config,
            'performance': {
                '初始资金': f"{initial_equity:,.2f}",
                '期末净值': f"{final_equity:,.2f}",
                '总收益率': f"{total_return:.2%}",
                '最大回撤': f"{max_drawdown:.2%}",
                '夏普比率': f"{sharpe_ratio:.4f}",
                '总交易次数': len(self.trades),
                '买入次数': len(buy_trades),
                '卖出次数': len(sell_trades),
                '胜率': f"{win_rate:.2%}",
                '总手续费': f"{self.total_commission:.2f}"
            },
            'raw_data': {
                'equity_curve': equity_df,
                'trades': pd.DataFrame(self.trades) if self.trades else pd.DataFrame(),
                'signals': pd.DataFrame(self.signals) if self.signals else pd.DataFrame()
            }
        }
        
        return results

    def plot_results(self, results: Dict = None, save_path: str = None) -> None:
        """绘制回测结果图表"""
        if results is None:
            logger.error("没有回测结果数据")
            return
            
        if 'raw_data' not in results:
            logger.error("回测结果中缺少原始数据")
            return
            
        try:
            # 设置matplotlib后端，避免GUI问题
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端
            
            equity_df = results['raw_data']['equity_curve']
            trades_df = results['raw_data']['trades']
            signals_df = results['raw_data']['signals']
            
            if equity_df.empty:
                logger.error("净值曲线数据为空")
                return
            
            # 创建图表
            fig, axes = plt.subplots(3, 1, figsize=(15, 12))
            fig.suptitle(f'增强版策略回测结果 - {self.config.symbol}', fontsize=16, fontweight='bold')
            
            # 1. 净值曲线和价格走势
            ax1 = axes[0]
            ax1_twin = ax1.twinx()
            
            # 净值曲线
            ax1.plot(equity_df['time'], equity_df['equity'], 'b-', linewidth=2, label='净值曲线')
            ax1.axhline(y=self.config.initial_capital, color='gray', linestyle='--', alpha=0.7, label='初始资金')
            ax1.set_ylabel('净值 (元)', color='b')
            ax1.tick_params(axis='y', labelcolor='b')
            ax1.legend(loc='upper left')
            ax1.grid(True, alpha=0.3)
            
            # 价格走势
            ax1_twin.plot(equity_df['time'], equity_df['price'], 'r-', alpha=0.7, linewidth=1, label='价格')
            ax1_twin.set_ylabel('价格 (元)', color='r')
            ax1_twin.tick_params(axis='y', labelcolor='r')
            ax1_twin.legend(loc='upper right')
            
            # 标记交易点
            if not trades_df.empty:
                buy_trades = trades_df[trades_df['type'] == 'BUY']
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                
                if not buy_trades.empty:
                    ax1_twin.scatter(buy_trades['time'], buy_trades['price'], 
                                   color='green', marker='^', s=100, alpha=0.8, 
                                   label=f'买入 ({len(buy_trades)}次)', zorder=5)
                
                if not sell_trades.empty:
                    ax1_twin.scatter(sell_trades['time'], sell_trades['price'], 
                                   color='red', marker='v', s=100, alpha=0.8, 
                                   label=f'卖出 ({len(sell_trades)}次)', zorder=5)
                
                ax1_twin.legend(loc='center right')
            
            ax1.set_title('净值曲线与价格走势')
            
            # 2. 仓位变化
            ax2 = axes[1]
            ax2.plot(equity_df['time'], equity_df['position'], 'g-', linewidth=2, label='持仓数量')
            ax2.fill_between(equity_df['time'], equity_df['position'], alpha=0.3, color='green')
            ax2.set_ylabel('持仓数量')
            ax2.set_title('仓位变化')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 交易信号
            ax3 = axes[2]
            if not signals_df.empty:
                ax3.plot(signals_df['time'], signals_df['signal'], 'purple', linewidth=1, alpha=0.7, label='交易信号')
                ax3.axhline(y=-self.config.buy_trigger_drop, color='red', linestyle='--', alpha=0.7, 
                           label=f'买入阈值 ({-self.config.buy_trigger_drop:.1%})')
                ax3.fill_between(signals_df['time'], signals_df['signal'], -self.config.buy_trigger_drop, 
                               where=(signals_df['signal'] <= -self.config.buy_trigger_drop), 
                               color='red', alpha=0.2, label='买入区域')
            
            ax3.set_ylabel('信号值')
            ax3.set_xlabel('时间')
            ax3.set_title('交易信号')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存或显示图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"图表已保存到: {save_path}")
            else:
                # 默认保存到当前目录
                default_path = f"backtest_result_{self.config.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                plt.savefig(default_path, dpi=300, bbox_inches='tight')
                logger.info(f"图表已保存到: {default_path}")
            
            plt.close(fig)  # 关闭图表释放内存
                
        except Exception as e:
            logger.error(f"绘图失败: {e}")
            import traceback
            traceback.print_exc()


def run_enhanced_backtest(config: BacktestConfig) -> dict:
    """运行增强版回测的便捷函数"""
    backtest = EnhancedBacktest(config)
    return backtest.run_backtest()


def main():
    """主函数"""
    import argparse

    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")

    parser = argparse.ArgumentParser(description="增强版策略回测")
    parser.add_argument("--symbol", default="159740", help="交易标的")
    parser.add_argument("--start-date", default="2025-08-25", help="开始日期")
    parser.add_argument("--end-date", default="2025-08-27", help="结束日期")
    parser.add_argument("--initial-capital", type=float, default=1000000, help="初始资金")

    args = parser.parse_args()

    # 创建回测配置
    config = BacktestConfig(
        symbol=args.symbol,
        start_date=args.start_date,
        end_date=args.end_date,
        initial_capital=args.initial_capital
    )

    # 运行回测
    results = run_enhanced_backtest(config)

    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return

    # 显示结果
    print("\n=== 增强版策略回测结果 ===")
    print(f"标的: {config.symbol}")
    print(f"时间: {config.start_date} 到 {config.end_date}")
    print("\n性能指标:")
    for key, value in results['performance'].items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()