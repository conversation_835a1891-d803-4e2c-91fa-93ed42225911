#!/usr/bin/env python3
"""
简化前端配置界面
适配新的简化分批止盈逻辑
"""

import streamlit as st
from strategy_config import StrategyConfig

def render_simplified_profit_taking_config():
    """渲染简化的分批止盈配置界面"""
    
    st.sidebar.subheader("💰 简化分批止盈设置")
    
    # 基础止盈目标
    profit_config = StrategyConfig.get_streamlit_config('profit_target')
    profit_target = st.sidebar.slider(
        "基础止盈目标", 
        min_value=profit_config['min_value'] * 100,
        max_value=profit_config['max_value'] * 100,
        value=profit_config['value'] * 100, 
        step=profit_config['step'] * 100,
        format="%.2f%%",
        help="基础止盈目标，其他级别基于此计算"
    ) / 100
    
    # 简化的止盈级别设置
    st.sidebar.write("**止盈级别设置**")
    
    col1, col2 = st.sidebar.columns(2)
    
    with col1:
        level1_multiplier = st.slider(
            "第一级倍数",
            min_value=0.5,
            max_value=2.0,
            value=1.2,  # 默认1.2倍，即0.3%
            step=0.1,
            format="%.1fx"
        )
        
        level2_multiplier = st.slider(
            "第二级倍数",
            min_value=1.0,
            max_value=3.0,
            value=2.4,  # 默认2.4倍，即0.6%
            step=0.1,
            format="%.1fx"
        )
        
        level3_multiplier = st.slider(
            "第三级倍数",
            min_value=2.0,
            max_value=5.0,
            value=4.8,  # 默认4.8倍，即1.2%
            step=0.1,
            format="%.1fx"
        )
    
    with col2:
        level1_sell_ratio = st.slider(
            "第一级卖出",
            min_value=10,
            max_value=50,
            value=30,
            step=5,
            format="%d%%"
        ) / 100
        
        level2_sell_ratio = st.slider(
            "第二级卖出",
            min_value=20,
            max_value=70,
            value=50,
            step=5,
            format="%d%%"
        ) / 100
        
        level3_sell_ratio = st.slider(
            "第三级卖出",
            min_value=50,
            max_value=100,
            value=70,
            step=5,
            format="%d%%"
        ) / 100
    
    # 计算实际止盈阈值
    level1_threshold = profit_target * level1_multiplier
    level2_threshold = profit_target * level2_multiplier
    level3_threshold = profit_target * level3_multiplier
    
    # 显示计算结果
    st.sidebar.write("**计算结果预览**")
    st.sidebar.write(f"第一级止盈: {level1_threshold:.3f} ({level1_threshold:.2%}) → 卖出{level1_sell_ratio:.0%}")
    st.sidebar.write(f"第二级止盈: {level2_threshold:.3f} ({level2_threshold:.2%}) → 卖出{level2_sell_ratio:.0%}")
    st.sidebar.write(f"第三级止盈: {level3_threshold:.3f} ({level3_threshold:.2%}) → 卖出{level3_sell_ratio:.0%}")
    
    # 验证配置合理性
    if level1_threshold >= level2_threshold or level2_threshold >= level3_threshold:
        st.sidebar.error("⚠️ 止盈级别设置不合理，请确保递增顺序")
    
    if level1_sell_ratio >= level2_sell_ratio or level2_sell_ratio >= level3_sell_ratio:
        st.sidebar.warning("⚠️ 建议卖出比例递增设置")
    
    return {
        'profit_target': profit_target,
        'level1_threshold': level1_threshold,
        'level2_threshold': level2_threshold,
        'level3_threshold': level3_threshold,
        'level1_sell_ratio': level1_sell_ratio,
        'level2_sell_ratio': level2_sell_ratio,
        'level3_sell_ratio': level3_sell_ratio,
        # 为了兼容现有系统，也返回倍数参数
        'partial_profit_multiplier1': level1_multiplier,
        'partial_profit_multiplier2': level2_multiplier,
        'partial_profit_multiplier3': level3_multiplier,
        'partial_sell_ratio1': level1_sell_ratio,
        'partial_sell_ratio2': level2_sell_ratio,
        'partial_sell_ratio3': level3_sell_ratio
    }

def render_profit_taking_status(position_info):
    """渲染止盈状态显示"""
    if not position_info or position_info.get('total_quantity', 0) <= 0:
        return
    
    st.subheader("📊 当前止盈状态")
    
    current_profit_rate = position_info.get('profit_rate', 0)
    profit_level_reached = position_info.get('profit_level_reached', 0)
    
    # 创建进度条显示
    col1, col2, col3 = st.columns(3)
    
    with col1:
        level1_reached = profit_level_reached >= 1
        st.metric(
            "第一级止盈",
            "已触发" if level1_reached else "未触发",
            delta="30%" if level1_reached else None
        )
    
    with col2:
        level2_reached = profit_level_reached >= 2
        st.metric(
            "第二级止盈",
            "已触发" if level2_reached else "未触发",
            delta="50%" if level2_reached else None
        )
    
    with col3:
        level3_reached = profit_level_reached >= 3
        st.metric(
            "第三级止盈",
            "已触发" if level3_reached else "未触发",
            delta="70%" if level3_reached else None
        )
    
    # 显示当前收益率
    st.write(f"**当前收益率**: {current_profit_rate:.2%}")
    
    # 进度条
    progress_value = min(1.0, max(0.0, current_profit_rate / 0.012))  # 假设1.2%为满进度
    st.progress(progress_value)

# 使用示例
def example_streamlit_app():
    """示例Streamlit应用"""
    st.title("简化分批止盈配置示例")
    
    # 渲染配置界面
    config = render_simplified_profit_taking_config()
    
    # 显示配置
    st.write("### 当前配置")
    st.json(config)
    
    # 模拟持仓状态显示
    st.write("### 模拟持仓状态")
    mock_position = {
        'total_quantity': 10000,
        'profit_rate': 0.008,  # 0.8%收益率
        'profit_level_reached': 1  # 已触发第一级
    }
    render_profit_taking_status(mock_position)

if __name__ == "__main__":
    example_streamlit_app()
