#!/usr/bin/env python3
"""
分析实际交易记录
"""

from backtest_enhanced import BacktestConfig, EnhancedBacktest
import pandas as pd
import logging

def analyze_actual_trades():
    """分析实际交易记录"""
    
    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
    
    print("=== 分析实际交易记录 ===")
    
    # 使用与实际回测相同的配置
    config = BacktestConfig(
        symbol="159740",
        start_date="2025-08-25",
        end_date="2025-08-27",
        initial_capital=1_000_000.0
    )
    
    print(f"配置参数:")
    print(f"  止损线: {config.stop_loss} ({config.stop_loss:.2%})")
    print(f"  止盈目标: {config.profit_target} ({config.profit_target:.2%})")
    
    # 运行回测
    backtest = EnhancedBacktest(config)
    results = backtest.run_backtest()
    
    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return
    
    # 分析交易记录
    trades_df = results['raw_data']['trades']
    
    if trades_df.empty:
        print("没有交易记录")
        return
    
    print(f"\n=== 交易记录分析 ===")
    print(f"总交易次数: {len(trades_df)}")
    
    for idx, trade in trades_df.iterrows():
        print(f"\n交易 {idx}:")
        print(f"  时间: {trade['time']}")
        print(f"  类型: {trade['type']}")
        print(f"  数量: {trade['quantity']}")
        print(f"  价格: {trade['price']:.4f}")
        print(f"  实际价格: {trade['actual_price']:.4f}")
        print(f"  原因: {trade['reason']}")
        
        if trade['type'] == 'SELL' and 'pnl' in trade:
            print(f"  盈亏: {trade['pnl']:.2f}")
    
    # 分析卖出交易的具体情况
    sell_trades = trades_df[trades_df['type'] == 'SELL']
    
    if not sell_trades.empty:
        print(f"\n=== 卖出交易详细分析 ===")
        
        for idx, sell_trade in sell_trades.iterrows():
            print(f"\n卖出交易 {idx}:")
            print(f"  原因: {sell_trade['reason']}")
            
            # 如果是止损，分析具体的收益率计算
            if '止损' in sell_trade['reason']:
                # 找到对应的买入交易
                buy_trades = trades_df[(trades_df['type'] == 'BUY') & (trades_df['time'] < sell_trade['time'])]
                
                if not buy_trades.empty:
                    last_buy = buy_trades.iloc[-1]
                    buy_price = last_buy['actual_price']
                    sell_price = sell_trade['actual_price']
                    
                    actual_profit_rate = (sell_price - buy_price) / buy_price
                    
                    print(f"  买入价格: {buy_price:.4f}")
                    print(f"  卖出价格: {sell_price:.4f}")
                    print(f"  实际收益率: {actual_profit_rate:.4f} ({actual_profit_rate:.2%})")
                    print(f"  配置止损线: {config.stop_loss:.4f} ({config.stop_loss:.2%})")
                    
                    if actual_profit_rate > config.stop_loss:
                        print(f"  ⚠️  警告: 实际收益率({actual_profit_rate:.2%}) > 止损线({config.stop_loss:.2%})，不应该触发止损！")

if __name__ == "__main__":
    analyze_actual_trades()