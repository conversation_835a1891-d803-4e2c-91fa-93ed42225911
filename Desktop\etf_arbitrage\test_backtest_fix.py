#!/usr/bin/env python3
"""
测试回测修复效果
"""

import pandas as pd
from backtest_enhanced import run_enhanced_backtest, BacktestConfig

def test_backtest_fix():
    """测试回测修复效果"""
    
    print("=== 测试回测修复效果 ===")
    
    # 配置参数 - 使用简单配置
    config = BacktestConfig(
        symbol='159740',
        start_date='2025-08-25',
        end_date='2025-09-01',
        initial_capital=1000000
    )

    print(f"配置参数:")
    print(f"  标的: {config.symbol}")
    print(f"  初始资金: {config.initial_capital:,.0f}")
    print(f"  回测期间: {config.start_date} 到 {config.end_date}")
    
    # 运行回测
    print("\n正在运行回测...")
    results = run_enhanced_backtest(config)
    
    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return
    
    # 显示结果
    print("\n=== 回测结果 ===")
    perf = results['performance']
    
    print(f"总收益率: {perf['总收益率']}")
    print(f"最大回撤: {perf['最大回撤']}")
    print(f"胜率: {perf['胜率']}")
    print(f"总交易次数: {perf['总交易次数']}")
    print(f"买入次数: {perf['买入次数']}")
    print(f"卖出次数: {perf['卖出次数']}")
    
    # 验证数据一致性
    print("\n=== 数据一致性验证 ===")
    
    # 检查交易数据
    trades_df = results['raw_data']['trades']
    if not trades_df.empty:
        sell_trades = trades_df[trades_df['type'] == 'SELL']
        if not sell_trades.empty and 'pnl' in sell_trades.columns:
            profitable_trades = len(sell_trades[sell_trades['pnl'] > 0])
            total_sell_trades = len(sell_trades)
            calculated_win_rate = profitable_trades / total_sell_trades if total_sell_trades > 0 else 0
            
            print(f"卖出交易数: {total_sell_trades}")
            print(f"盈利交易数: {profitable_trades}")
            print(f"计算胜率: {calculated_win_rate:.2%}")
            print(f"显示胜率: {perf['胜率']}")
            
            # 检查是否一致
            displayed_win_rate = float(perf['胜率'].rstrip('%')) / 100
            if abs(calculated_win_rate - displayed_win_rate) < 0.001:
                print("✅ 胜率计算正确")
            else:
                print("❌ 胜率计算不一致")
        else:
            print("⚠️ 没有卖出交易或缺少pnl数据")
    else:
        print("⚠️ 没有交易数据")
    
    # 检查净值计算
    equity_df = results['raw_data']['equity_curve']
    if not equity_df.empty:
        initial_equity = config.initial_capital
        final_equity = equity_df['equity'].iloc[-1]
        calculated_return = (final_equity - initial_equity) / initial_equity
        
        print(f"\n初始资金: {initial_equity:,.2f}")
        print(f"期末净值: {final_equity:,.2f}")
        print(f"计算收益率: {calculated_return:.2%}")
        print(f"显示收益率: {perf['总收益率']}")
        
        # 检查是否一致
        displayed_return = float(perf['总收益率'].rstrip('%')) / 100
        if abs(calculated_return - displayed_return) < 0.001:
            print("✅ 收益率计算正确")
        else:
            print("❌ 收益率计算不一致")
    else:
        print("⚠️ 没有净值数据")
    
    # 导出CSV验证
    if not trades_df.empty:
        csv_file = '2025-09-01T03-20_export_test.csv'
        trades_df.to_csv(csv_file, index=True)
        print(f"\n交易记录已导出到: {csv_file}")
        
        # 验证CSV数据
        csv_df = pd.read_csv(csv_file)
        csv_total_pnl = csv_df['pnl'].sum()
        print(f"CSV总盈亏: {csv_total_pnl:.2f}")
        
        csv_profit_trades = len(csv_df[csv_df['pnl'] > 0])
        csv_total_trades = len(csv_df[csv_df['pnl'] != 0])
        csv_win_rate = csv_profit_trades / csv_total_trades if csv_total_trades > 0 else 0
        print(f"CSV胜率: {csv_win_rate:.2%}")

if __name__ == "__main__":
    test_backtest_fix()
