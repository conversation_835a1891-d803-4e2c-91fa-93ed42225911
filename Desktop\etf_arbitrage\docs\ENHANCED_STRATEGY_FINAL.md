# 增强版ETF套利策略 - 最终版本

## 📁 核心文件

### 策略引擎
- **`strategy_engine_enhanced.py`** - 增强版策略引擎（实盘运行）
- **`strategy_config.py`** - 策略配置管理
- **`run_enhanced_strategy.py`** - 策略启动脚本

### 回测系统
- **`backtest_enhanced.py`** - 增强版回测引擎
- **`run_enhanced_backtest.py`** - 回测启动脚本

### 监控工具
- **`strategy_monitor.py`** - 策略性能监控

### 文档
- **`ENHANCED_STRATEGY_README.md`** - 详细使用说明
- **`ENHANCED_BACKTEST_README.md`** - 回测使用说明

## 🚀 快速开始

### 1. 运行增强版策略
```bash
# 实盘运行
python strategy_engine_enhanced.py --symbol 159740

# 或使用启动脚本
python run_enhanced_strategy.py --symbol 159740 --mode enhanced
```

### 2. 运行回测
```bash
# 默认配置回测
python run_enhanced_backtest.py default

# 保守配置回测
python run_enhanced_backtest.py conservative

# 激进配置回测
python run_enhanced_backtest.py aggressive

# 直接运行回测引擎
python backtest_enhanced.py --symbol 159740 --start-date 2025-08-25 --end-date 2025-08-27
```

### 3. 性能监控
```bash
python strategy_monitor.py --symbol 159740 --days 7
```

## ⭐ 主要特性

### 风险控制
- ✅ 止损机制（-2%止损 + 时间止损）
- ✅ 日内损失限制和最大回撤控制
- ✅ 动态风险预算管理

### 智能交易
- ✅ 分层买入策略（40%/35%/25%）
- ✅ 分批卖出策略（0.3%/0.6%/1.2%分批止盈）
- ✅ 动态参数调整（基于市场波动率）

### 监控分析
- ✅ 实时性能监控
- ✅ 详细交易统计
- ✅ 风险指标计算
- ✅ 图表可视化

## 📊 回测验证

最新回测结果（2025-08-25 到 2025-08-27）：
- **总收益率**: -0.06%
- **最大回撤**: -0.49%
- **胜率**: 100.00%
- **交易次数**: 5次
- **夏普比率**: -0.7187

## 🔧 配置选项

### 默认配置
- 买入触发: -0.6%
- 止盈目标: 0.6%
- 止损线: -2.0%
- 最大持仓时间: 3600秒

### 保守配置
- 买入触发: -0.4%
- 止损线: -1.5%
- 最大持仓时间: 1800秒

### 激进配置
- 买入触发: -0.8%
- 止损线: -2.5%
- 最大持仓时间: 7200秒

## 📈 策略逻辑

### 买入条件
1. 20个tick收益率 ≤ 买入阈值
2. 当前仓位 < 最大仓位
3. 风险检查通过

### 卖出条件
1. **分批止盈**: 0.3%、0.6%、1.2%分批卖出
2. **止损**: 持仓亏损达到-2%
3. **时间止损**: 持仓时间超过1小时

## ⚠️ 注意事项

1. **数据依赖**: 需要数据库中有实时tick数据
2. **参数调整**: 建议先回测验证再实盘运行
3. **风险控制**: 严格遵守风险管理规则
4. **监控报警**: 设置关键指标的报警阈值

## 🎯 下一步优化

1. 机器学习参数优化
2. 多标的组合策略
3. 更精细的市场状态识别
4. 实时风险预警系统

---

**版本**: v2.0 增强版  
**更新时间**: 2025-08-27  
**状态**: 生产就绪 ✅