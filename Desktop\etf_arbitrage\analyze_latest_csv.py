#!/usr/bin/env python3
"""
分析最新CSV文件的数据一致性问题
"""

import pandas as pd

def analyze_latest_csv():
    """分析最新CSV文件"""
    
    print("=== 分析最新CSV文件 ===")
    
    # 读取最新CSV
    csv_file = '2025-09-01T16-54_export.csv'
    df = pd.read_csv(csv_file)
    
    print(f"CSV文件: {csv_file}")
    print(f"总交易数: {len(df)}")
    
    # 分析买卖交易
    buy_trades = df[df['type'] == 'BUY']
    sell_trades = df[df['type'] == 'SELL']
    
    print(f"\n📊 交易结构:")
    print(f"买入交易: {len(buy_trades)}笔")
    print(f"卖出交易: {len(sell_trades)}笔")
    
    # 分析胜率
    if len(sell_trades) > 0 and 'pnl' in sell_trades.columns:
        profitable = len(sell_trades[sell_trades['pnl'] > 0])
        losing = len(sell_trades[sell_trades['pnl'] < 0])
        breakeven = len(sell_trades[sell_trades['pnl'] == 0])
        
        win_rate = profitable / len(sell_trades) * 100
        
        print(f"\n💰 盈亏分析:")
        print(f"盈利交易: {profitable}笔")
        print(f"亏损交易: {losing}笔")
        print(f"盈亏平衡: {breakeven}笔")
        print(f"胜率: {win_rate:.2f}%")
        
        # PnL统计
        total_pnl = sell_trades['pnl'].sum()
        min_pnl = sell_trades['pnl'].min()
        max_pnl = sell_trades['pnl'].max()
        avg_pnl = sell_trades['pnl'].mean()
        
        print(f"\n📈 PnL统计:")
        print(f"总盈亏: {total_pnl:.2f}")
        print(f"最小PnL: {min_pnl:.2f}")
        print(f"最大PnL: {max_pnl:.2f}")
        print(f"平均PnL: {avg_pnl:.2f}")
        
        # 分析交易原因
        print(f"\n🎯 交易原因分析:")
        reasons = sell_trades['reason'].value_counts()
        for reason, count in reasons.items():
            reason_pnl = sell_trades[sell_trades['reason'] == reason]['pnl'].sum()
            print(f"{reason}: {count}笔, 盈亏: {reason_pnl:.2f}")
    
    # 与界面显示对比
    print(f"\n🔍 与界面显示对比:")
    print(f"界面显示 vs CSV实际:")
    print(f"总交易次数: 44 vs {len(df)}")
    print(f"卖出次数: 16 vs {len(sell_trades)}")
    
    if len(sell_trades) > 0:
        csv_win_rate = len(sell_trades[sell_trades['pnl'] > 0]) / len(sell_trades) * 100
        print(f"胜率: 100.00% vs {csv_win_rate:.2f}%")
        
        if csv_win_rate == 100.0:
            print("✅ 胜率一致 - 所有卖出交易都是盈利的")
        else:
            print("❌ 胜率不一致")
    
    # 检查是否有问题
    print(f"\n⚠️ 潜在问题:")
    
    # 1. 检查是否所有交易都盈利
    if len(sell_trades) > 0 and len(sell_trades[sell_trades['pnl'] <= 0]) == 0:
        print("1. 所有卖出交易都是盈利的 - 这可能表明:")
        print("   - 策略参数过于保守")
        print("   - 止损逻辑未正确执行")
        print("   - 回测期间市场表现异常好")
    
    # 2. 检查交易数量
    if len(df) == 44:
        print("2. 交易数量较少 - 可能原因:")
        print("   - 买入阈值设置过严格(-0.80%)")
        print("   - 回测期间价格波动较小")
        print("   - 策略触发条件较少")
    
    # 3. 检查是否有止损交易
    stop_loss_trades = sell_trades[sell_trades['reason'].str.contains('止损', na=False)]
    if len(stop_loss_trades) == 0:
        print("3. 没有止损交易 - 可能原因:")
        print("   - 止损线设置过宽(-1.50%)")
        print("   - 回测期间没有大幅下跌")
        print("   - 收盘前平仓机制起作用")
    
    # 4. 分析收盘平仓
    close_trades = sell_trades[sell_trades['reason'].str.contains('收盘', na=False)]
    if len(close_trades) > 0:
        close_pnl = close_trades['pnl'].sum()
        print(f"4. 收盘平仓: {len(close_trades)}笔, 盈亏: {close_pnl:.2f}")
    
    # 5. 分析日期分布
    print(f"\n📅 交易日期分析:")
    df['date'] = pd.to_datetime(df['time']).dt.date
    daily_counts = df.groupby('date').size()
    for date, count in daily_counts.items():
        date_trades = df[df['date'] == date]
        date_pnl = date_trades['pnl'].sum() if 'pnl' in date_trades.columns else 0
        print(f"{date}: {count}笔交易, 盈亏: {date_pnl:.2f}")

if __name__ == "__main__":
    analyze_latest_csv()
