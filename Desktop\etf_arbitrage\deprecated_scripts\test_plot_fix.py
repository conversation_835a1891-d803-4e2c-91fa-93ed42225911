#!/usr/bin/env python3
"""
测试绘图修复
"""

from backtest_enhanced import BacktestConfig, EnhancedBacktest
import logging

def test_plot_fix():
    """测试绘图功能修复"""
    
    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
    
    print("=== 测试增强版回测绘图修复 ===")
    
    # 创建配置
    config = BacktestConfig(
        symbol="159740",
        start_date="2025-08-25",
        end_date="2025-08-27",
        initial_capital=1_000_000.0
    )
    
    # 运行回测
    print("开始运行回测...")
    backtest = EnhancedBacktest(config)
    results = backtest.run_backtest()
    
    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return False
    
    # 显示结果
    print("\n=== 回测结果 ===")
    for key, value in results['performance'].items():
        print(f"  {key}: {value}")
    
    # 测试绘图功能
    print("\n=== 测试绘图功能 ===")
    try:
        backtest.plot_results(results)
        print("✅ 绘图功能正常工作")
        return True
    except Exception as e:
        print(f"❌ 绘图失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_plot_fix()
    if success:
        print("\n🎉 修复成功！增强版回测现在可以正常绘图了。")
    else:
        print("\n❌ 修复失败，需要进一步调试。")