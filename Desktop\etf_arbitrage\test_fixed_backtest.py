#!/usr/bin/env python3
"""
测试修复后的回测引擎
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backtest_enhanced import EnhancedBacktest, BacktestConfig
from datetime import datetime, date
import pandas as pd

def test_fixed_backtest():
    """测试修复后的回测引擎"""
    
    print("=== 测试修复后的回测引擎 ===")
    
    # 配置参数（使用默认配置）
    config = BacktestConfig(
        symbol='159740',
        start_date=date(2025, 8, 25),
        end_date=date(2025, 9, 1),
        initial_capital=1000000.0
    )

    print(f"配置参数:")
    print(f"  交易标的: {config.symbol}")
    print(f"  回测期间: {config.start_date} ~ {config.end_date}")
    print(f"  初始资金: {config.initial_capital:,}元")
    
    # 运行回测
    backtest = EnhancedBacktest(config)
    results = backtest.run_backtest()
    
    if 'error' in results:
        print(f"❌ 回测失败: {results['error']}")
        return
    
    # 分析结果
    perf = results['performance']
    print(f"\n📊 回测结果:")
    print(f"初始资金: {perf['初始资金']}")
    print(f"期末净值: {perf['期末净值']}")
    print(f"总收益率: {perf['总收益率']}")
    print(f"最大回撤: {perf['最大回撤']}")
    print(f"胜率: {perf['胜率']}")
    print(f"总交易次数: {perf['总交易次数']}")
    print(f"总手续费: {perf['总手续费']}")
    
    # 详细分析
    equity_df = results['raw_data']['equity_curve']
    trades_df = results['raw_data']['trades']
    
    if not equity_df.empty:
        print(f"\n📈 净值曲线分析:")
        print(f"数据点数: {len(equity_df)}")
        
        final_row = equity_df.iloc[-1]
        print(f"最终净值: {final_row['equity']:.2f}")
        
        if 'cash' in final_row:
            print(f"现金余额: {final_row['cash']:.2f}")
        if 'market_value' in final_row:
            print(f"持仓市值: {final_row['market_value']:.2f}")
        if 'realized_pnl' in final_row:
            print(f"已实现盈亏: {final_row['realized_pnl']:.2f}")
        if 'total_commission' in final_row:
            print(f"累计手续费: {final_row['total_commission']:.2f}")
    
    if not trades_df.empty:
        print(f"\n💰 交易分析:")
        buy_trades = trades_df[trades_df['type'] == 'BUY']
        sell_trades = trades_df[trades_df['type'] == 'SELL']
        
        print(f"买入交易: {len(buy_trades)}笔")
        print(f"卖出交易: {len(sell_trades)}笔")
        
        if not sell_trades.empty and 'pnl' in sell_trades.columns:
            total_pnl = sell_trades['pnl'].sum()
            profitable = len(sell_trades[sell_trades['pnl'] > 0])
            losing = len(sell_trades[sell_trades['pnl'] <= 0])
            
            print(f"总已实现盈亏: {total_pnl:.2f}元")
            print(f"盈利交易: {profitable}笔")
            print(f"亏损交易: {losing}笔")
            print(f"实际胜率: {profitable/len(sell_trades)*100:.2f}%")
    
    # 验证净值计算
    print(f"\n🧮 净值计算验证:")
    initial_capital = config.initial_capital
    
    if not trades_df.empty:
        sell_trades = trades_df[trades_df['type'] == 'SELL']
        total_realized_pnl = sell_trades['pnl'].sum() if not sell_trades.empty else 0
        total_commission = float(perf['总手续费'].replace(',', ''))
        
        # 检查是否有剩余持仓
        if not equity_df.empty:
            final_row = equity_df.iloc[-1]
            final_position = final_row['position']
            
            if final_position > 0:
                print("⚠️ 回测结束时仍有持仓，这可能影响收益率计算")
                final_price = final_row['price']
                market_value = final_position * final_price
                print(f"剩余持仓: {final_position:,}股")
                print(f"最终价格: {final_price:.4f}")
                print(f"持仓市值: {market_value:.2f}元")
            else:
                print("✅ 回测结束时无持仓")
        
        # 计算理论净值
        theoretical_equity = initial_capital + total_realized_pnl - total_commission
        actual_equity = float(perf['期末净值'].replace(',', ''))
        
        print(f"理论净值: {initial_capital:,} + {total_realized_pnl:.2f} - {total_commission:.2f} = {theoretical_equity:.2f}")
        print(f"实际净值: {actual_equity:.2f}")
        print(f"差异: {abs(theoretical_equity - actual_equity):.2f}")
        
        # 计算理论收益率
        theoretical_return = (theoretical_equity - initial_capital) / initial_capital * 100
        actual_return = float(perf['总收益率'].rstrip('%'))
        
        print(f"理论收益率: {theoretical_return:.2f}%")
        print(f"实际收益率: {actual_return:.2f}%")
        print(f"收益率差异: {abs(theoretical_return - actual_return):.2f}%")
        
        if abs(theoretical_return - actual_return) < 0.01:
            print("✅ 收益率计算正确")
        else:
            print("❌ 收益率计算有误")

if __name__ == "__main__":
    test_fixed_backtest()
