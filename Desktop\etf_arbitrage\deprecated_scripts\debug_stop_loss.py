#!/usr/bin/env python3
"""
调试止损逻辑
"""

from backtest_enhanced import BacktestConfig, EnhancedBacktest
from strategy_engine_enhanced import STOP_LOSS, PROFIT_TARGET
import logging

def debug_stop_loss():
    """调试止损逻辑"""
    
    logging.basicConfig(level=logging.DEBUG, format="%(asctime)s %(levelname)s %(message)s")
    
    print("=== 调试止损逻辑 ===")
    
    # 创建配置
    config = BacktestConfig()
    
    print(f"配置参数:")
    print(f"  STOP_LOSS (常量): {STOP_LOSS}")
    print(f"  PROFIT_TARGET (常量): {PROFIT_TARGET}")
    print(f"  config.stop_loss: {config.stop_loss}")
    print(f"  config.profit_target: {config.profit_target}")
    print(f"  config.partial_profit_multiplier1: {config.partial_profit_multiplier1}")
    print(f"  config.partial_profit_multiplier2: {config.partial_profit_multiplier2}")
    print(f"  config.partial_profit_multiplier3: {config.partial_profit_multiplier3}")
    
    # 计算分批止盈目标
    partial_levels = [
        config.profit_target * config.partial_profit_multiplier1,
        config.profit_target * config.partial_profit_multiplier2, 
        config.profit_target * config.partial_profit_multiplier3
    ]
    
    print(f"\n分批止盈目标:")
    for i, level in enumerate(partial_levels):
        print(f"  第{i+1}次止盈: {level:.4f} ({level:.2%})")
    
    print(f"\n止损目标: {config.stop_loss:.4f} ({config.stop_loss:.2%})")
    
    # 模拟一些收益率情况
    test_rates = [0.0002, 0.003, 0.006, 0.012, -0.01, -0.02, -0.03]
    
    print(f"\n模拟不同收益率的卖出判断:")
    backtest = EnhancedBacktest(config)
    
    for rate in test_rates:
        # 模拟仓位
        backtest.position.quantity = 1000000
        backtest.position.avg_cost = 4.5380
        backtest.position.total_cost = 4538000
        
        # 设置买入时间（确保超过最小持仓时间）
        from datetime import datetime, timedelta
        backtest.position.first_buy_time = datetime.now() - timedelta(seconds=60)  # 60秒前买入
        
        current_price = 4.5380 * (1 + rate)
        current_time = datetime.now()
        
        should_sell, sell_ratio, reason = backtest.should_sell(current_price, current_time)
        
        print(f"  收益率 {rate:.4f} ({rate:.2%}): 卖出={should_sell}, 比例={sell_ratio:.2f}, 原因='{reason}'")

if __name__ == "__main__":
    debug_stop_loss()