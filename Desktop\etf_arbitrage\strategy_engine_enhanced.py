import sqlite3
import sqlite3
import time
import logging
from typing import Optional, Tu<PERSON>, Dict, List
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import numpy as np
import pandas as pd

# 导入统一配置
from strategy_config import (
    StrategyConfig,
    BUY_TRIGGER_DROP, PROFIT_TARGET, STOP_LOSS, MAX_HOLD_TIME,
    MAX_POSITION, DAILY_LOSS_LIMIT, MAX_DRAWDOWN_LIMIT,
    LAYERS, PARTIAL_SELL_LEVELS, PARTIAL_SELL_RATIOS, MIN_HOLD_TIME
)

DB_PATH: str = "ticks.db"

logger = logging.getLogger(__name__)


class MarketRegime(Enum):
    TRENDING = "trending"
    RANGING = "ranging"
    VOLATILE = "volatile"


@dataclass
class PositionBatch:
    """持仓批次信息"""
    quantity: int
    cost: float  # 该批次的总成本
    buy_time: datetime
    partial_sold: List[bool] = None
    
    def __post_init__(self):
        if self.partial_sold is None:
            self.partial_sold = [False] * len(PARTIAL_SELL_LEVELS)
    
    @property
    def avg_cost(self) -> float:
        """计算该批次的平均成本"""
        return self.cost / self.quantity if self.quantity > 0 else 0.0
    
    def get_profit_rate(self, current_price: float) -> float:
        """计算该批次的收益率"""
        if self.quantity <= 0 or self.avg_cost <= 0:
            return 0.0
        return (current_price - self.avg_cost) / self.avg_cost


@dataclass
class Position:
    """持仓信息"""
    batches: List[PositionBatch] = None
    total_quantity: int = 0
    total_cost: float = 0.0
    first_buy_time: Optional[datetime] = None
    # 简化的止盈状态跟踪
    profit_level_reached: int = 0  # 0=未止盈, 1=第一级, 2=第二级, 3=第三级

    def __post_init__(self):
        if self.batches is None:
            self.batches = []
    
    def add_position(self, qty: int, price: float, timestamp: datetime):
        """增加持仓"""
        batch = PositionBatch(
            quantity=qty,
            cost=qty * price,
            buy_time=timestamp
        )
        self.batches.append(batch)

        self.total_quantity += qty
        self.total_cost += qty * price
        if self.first_buy_time is None:
            self.first_buy_time = timestamp

        # 智能重置止盈级别：如果是重新建仓或大幅加仓，重置止盈级别
        if self._should_reset_profit_level(qty):
            self.profit_level_reached = 0
    
    def reduce_position(self, qty: int) -> float:
        """减少持仓，返回减少的成本"""
        if qty >= self.total_quantity:
            # 全部卖出
            cost_reduced = self.total_cost
            self.batches.clear()
            self.total_quantity = 0
            self.total_cost = 0.0
            self.first_buy_time = None
            return cost_reduced
        else:
            # 部分卖出 - 按照先进先出原则
            cost_reduced = 0.0
            qty_to_sell = qty
            
            while qty_to_sell > 0 and self.batches:
                batch = self.batches[0]
                if batch.quantity <= qty_to_sell:
                    # 卖出整个批次
                    cost_reduced += batch.cost
                    qty_to_sell -= batch.quantity
                    self.batches.pop(0)
                else:
                    # 部分卖出批次
                    ratio = qty_to_sell / batch.quantity
                    cost_reduced += batch.cost * ratio
                    batch.quantity -= qty_to_sell
                    batch.cost -= batch.cost * ratio
                    qty_to_sell = 0
            
            self.total_quantity -= qty
            self.total_cost -= cost_reduced

            # 优化：清理空批次，减少内存占用
            self._cleanup_empty_batches()

            return cost_reduced
    
    def get_profit_rate(self, current_price: float) -> float:
        """计算整体收益率"""
        if self.total_quantity <= 0 or self.total_cost <= 0:
            return 0.0
        avg_cost = self.total_cost / self.total_quantity
        return (current_price - avg_cost) / avg_cost
    
    def get_hold_time(self, current_time: datetime = None) -> int:
        """获取持仓时间（秒）"""
        if self.first_buy_time is None:
            return 0
        # 如果提供了当前时间（回测模式），使用提供的时间；否则使用系统时间（实时模式）
        reference_time = current_time if current_time is not None else datetime.now()
        return int((reference_time - self.first_buy_time).total_seconds())
    
    def reset_partial_sold_flags(self):
        """重置止盈状态"""
        self.profit_level_reached = 0

    def _should_reset_profit_level(self, new_qty: int) -> bool:
        """判断是否应该重置止盈级别"""
        # 如果是首次买入，不需要重置
        if len(self.batches) <= 1:
            return False

        # 如果新买入数量占总仓位的比例超过30%，重置止盈级别
        old_quantity = self.total_quantity - new_qty
        if old_quantity == 0:
            return True

        new_ratio = new_qty / self.total_quantity
        return new_ratio >= 0.3  # 新买入占30%以上时重置

    def _cleanup_empty_batches(self):
        """清理空批次，优化内存使用"""
        self.batches = [batch for batch in self.batches if batch.quantity > 0]

    def check_partial_sell(self, current_price: float, config=None) -> Tuple[bool, int, float, str]:
        """
        简化的分批止盈检查
        返回 (是否卖出, 级别, 卖出比例, 原因)

        Args:
            current_price: 当前价格
            config: 可选的配置对象，如果提供则使用其参数，否则使用默认配置
        """
        if self.total_quantity <= 0:
            return False, -1, 0.0, ""

        # 计算整体收益率
        profit_rate = self.get_profit_rate(current_price)

        # 根据是否提供config来选择参数来源
        if config is not None:
            # 使用传入的配置参数
            profit_target = config.profit_target
            multiplier1 = config.partial_profit_multiplier1
            multiplier2 = config.partial_profit_multiplier2
            multiplier3 = config.partial_profit_multiplier3
            ratio1 = config.partial_sell_ratio1
            ratio2 = config.partial_sell_ratio2
            ratio3 = config.partial_sell_ratio3
        else:
            # 使用默认配置
            from strategy_config import _DEFAULT_CONFIG
            profit_target = _DEFAULT_CONFIG['profit_target']
            multiplier1 = _DEFAULT_CONFIG['partial_profit_multiplier1']
            multiplier2 = _DEFAULT_CONFIG['partial_profit_multiplier2']
            multiplier3 = _DEFAULT_CONFIG['partial_profit_multiplier3']
            ratio1 = _DEFAULT_CONFIG['partial_sell_ratio1']
            ratio2 = _DEFAULT_CONFIG['partial_sell_ratio2']
            ratio3 = _DEFAULT_CONFIG['partial_sell_ratio3']

        # 定义止盈级别和对应的卖出比例
        profit_levels = [
            (profit_target * multiplier1, ratio1, "第一次止盈"),
            (profit_target * multiplier2, ratio2, "第二次止盈"),
            (profit_target * multiplier3, ratio3, "第三次止盈")
        ]

        # 检查是否达到新的止盈级别
        for level_idx, (threshold, sell_ratio, reason) in enumerate(profit_levels):
            if profit_rate >= threshold and self.profit_level_reached <= level_idx:
                self.profit_level_reached = level_idx + 1
                return True, level_idx, sell_ratio, f"{reason}: {profit_rate:.4f}"

        return False, -1, 0.0, ""


class RiskManager:
    """风险管理器"""
    
    def __init__(self, initial_capital: float = 1_000_000.0):
        self.initial_capital = initial_capital
        self.daily_pnl = 0.0
        self.peak_equity = initial_capital
        self.current_equity = initial_capital
        self.daily_start_equity = initial_capital
        self.last_reset_date = datetime.now().date()
    
    def update_equity(self, new_equity: float):
        """更新净值"""
        # 检查是否需要重置日内统计
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_start_equity = self.current_equity
            self.last_reset_date = current_date
        
        self.current_equity = new_equity
        self.peak_equity = max(self.peak_equity, new_equity)
        self.daily_pnl = (new_equity - self.daily_start_equity) / self.daily_start_equity
    
    def check_risk_limits(self) -> bool:
        """检查是否触发风险限制"""
        # 计算当前回撤
        drawdown = (self.current_equity - self.peak_equity) / self.peak_equity
        
        # 检查风险限制
        if self.daily_pnl <= DAILY_LOSS_LIMIT:
            logger.warning(f"触发日损失限制: {self.daily_pnl:.4f}")
            return False
        
        if drawdown <= MAX_DRAWDOWN_LIMIT:
            logger.warning(f"触发最大回撤限制: {drawdown:.4f}")
            return False
        
        return True


class EnhancedStrategy:
    """增强版策略引擎"""
    
    def __init__(self, symbol: str, initial_capital: float = 1_000_000.0):
        self.symbol = symbol
        self.position = Position()
        self.risk_manager = RiskManager(initial_capital)
        self.current_params = {
            'buy_drop': BUY_TRIGGER_DROP,
            'profit_target': PROFIT_TARGET,
            'stop_loss': STOP_LOSS
        }
        self.last_market_update = datetime.now()
        self.poll_sec = 1.0
        self.is_running = False
        
    def _safe_return_20ticks(self, df: pd.DataFrame) -> float:
        """计算近5分钟内的20ticks价格波动率作为交易信号"""
        try:
            if df is None or df.shape[0] < 2:
                return 0.0
            
            prices = df["price"].astype(float)
            if prices.isnull().any() or len(prices) < 2:
                return 0.0
                
            # 计算最高价与最新价格之间的波动率
            max_price = prices.max()
            latest_price = prices.iloc[-1]  # 最新价格是最后一个价格点
            
            if latest_price <= 0:
                return 0.0
                
            # 波动率 = (最高价-最新价) / 最新价
            volatility = (max_price - latest_price) / latest_price
            
            if np.isnan(volatility) or np.isinf(volatility):
                return 0.0
                
            return float(volatility)
        except Exception as e:
            logger.error(f"计算20ticks波动率失败: {e}")
            return 0.0
    
    def _load_recent_ticks(self, conn: sqlite3.Connection, span_seconds: int = 600) -> pd.DataFrame:
        """读取近期tick数据"""
        try:
            since_iso = (datetime.now() - timedelta(seconds=span_seconds)).isoformat(timespec="seconds")
            # 使用signal_window参数限制获取的tick数量
            signal_window = self.current_params.get('signal_window', 20)  # 默认20个ticks
            df = pd.read_sql_query(
                "SELECT tick_time AS time, price, volume FROM ticks "
                "WHERE symbol=? AND tick_time>=? ORDER BY tick_time DESC LIMIT ?",
                conn,
                params=[self.symbol, since_iso, signal_window],
                parse_dates=["time"]
            )
            # 按时间升序排列，确保最新的数据在最后
            if not df.empty:
                df = df.sort_values("time")
            if df.empty:
                return df
            df["price"] = pd.to_numeric(df["price"], errors="coerce")
            df["volume"] = pd.to_numeric(df["volume"], errors="coerce").fillna(0).astype(int)
            return df.dropna(subset=["price"])
        except Exception as e:
            logger.error(f"读取近窗口ticks失败: {e}")
            return pd.DataFrame(columns=["time", "price", "volume"])
    
    def _ensure_signal_table(self, conn: sqlite3.Connection) -> None:
        try:
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute(
                """CREATE TABLE IF NOT EXISTS strategy_signals(
                    symbol TEXT NOT NULL,
                    ts TEXT NOT NULL,
                    signal TEXT NOT NULL,
                    quantity INTEGER,
                    price REAL,
                    reason TEXT,
                    PRIMARY KEY(symbol, ts)
                )"""
            )
            conn.commit()
        except Exception as e:
            logger.error(f"初始化signal表失败: {e}")
    
    def _upsert_signal(self, conn: sqlite3.Connection, ts: datetime, sig: str, 
                      qty: int = 0, price: float = 0.0, reason: str = "") -> None:
        """写入策略信号"""
        try:
            conn.execute(
                "INSERT OR REPLACE INTO strategy_signals(symbol, ts, signal, quantity, price, reason) VALUES (?, ?, ?, ?, ?, ?)",
                (self.symbol, ts.isoformat(timespec="seconds"), sig.upper(), qty, price, reason)
            )
            conn.commit()
        except Exception as e:
            logger.error(f"写入策略信号失败: {e}")
    
    def update_market_state(self, conn: sqlite3.Connection):
        """更新市场状态和动态参数"""
        # 每5分钟更新一次市场状态
        if (datetime.now() - self.last_market_update).total_seconds() < 300:
            return
        
        try:
            logger.info("更新市场状态...")
            self.last_market_update = datetime.now()
        except Exception as e:
            logger.error(f"更新市场状态失败: {e}")
    
    def should_buy(self, signal: float, current_price: float) -> bool:
        """判断是否应该买入"""
        if not self.risk_manager.check_risk_limits():
            return False
        
        if self.position.total_quantity >= MAX_POSITION:
            return False
        
        if current_price <= 0:
            return False
        
        # 如果当前有仓位，检查是否应该买入
        if self.position.total_quantity > 0:
            profit_rate = self.position.get_profit_rate(current_price)
            # 修改逻辑：只有在大幅盈利时才禁止买入，小幅盈利允许加仓
            if profit_rate > 0.02:  # 盈利超过2%时不买入
                return False
            # 如果当前小幅亏损，允许加仓（降低平均成本）
            # 只有在亏损很小时才禁止（避免频繁交易）
            if -0.005 < profit_rate <= 0:  # 亏损小于0.5%时不加仓
                return False
        
        buy_threshold = self.current_params.get('buy_drop', BUY_TRIGGER_DROP)
        return signal <= buy_threshold
    
    def should_sell(self, current_price: float) -> Tuple[bool, float, str]:
        """判断是否应该卖出，返回(是否卖出, 卖出比例, 原因)"""
        if self.position.total_quantity <= 0:
            return False, 0.0, ""
        
        profit_rate = self.position.get_profit_rate(current_price)
        hold_time = self.position.get_hold_time()  # 实时模式使用系统时间
        
        # 止损检查
        stop_loss_threshold = self.current_params.get('stop_loss', STOP_LOSS)
        if profit_rate <= stop_loss_threshold:
            return True, 1.0, f"止损: {profit_rate:.4f}"
        
        # 时间止损
        if hold_time >= MAX_HOLD_TIME:
            return True, 1.0, f"时间止损: {hold_time}s"
        
        # 分批止盈检查 - 实时模式使用默认配置
        partial_sell_result = self.position.check_partial_sell(current_price)
        if partial_sell_result[0]:
            # 找到满足条件的分批止盈
            _, level_idx, sell_ratio, reason = partial_sell_result
            return True, sell_ratio, reason
        
        return False, 0.0, ""
    
    def execute_buy(self, conn: sqlite3.Connection, current_price: float, ts: datetime):
        """执行买入"""
        # 从配置中获取position_size参数
        position_size = getattr(self, 'position_size', MAX_POSITION)
        remaining = MAX_POSITION - self.position.total_quantity
        total_bought = 0
        
        for pct in LAYERS:
            # 使用position_size参数替代MAX_POSITION计算每层买入数量
            qty = int(position_size * pct)
            if qty <= 0 or remaining <= 0:
                continue
            alloc = min(qty, remaining)
            
            self.position.add_position(alloc, current_price, ts)
            total_bought += alloc
            remaining -= alloc
        
        if total_bought > 0:
            self._upsert_signal(conn, ts, "B", total_bought, current_price, "分层买入")
            logger.info(f"买入执行: 数量={total_bought}, 价格={current_price:.4f}, "
                       f"总仓位={self.position.total_quantity}, 平均成本={self.position.get_profit_rate(current_price):.4f}")
    
    def execute_sell(self, conn: sqlite3.Connection, current_price: float, ts: datetime, 
                    sell_ratio: float, reason: str):
        """执行卖出"""
        sell_qty = int(self.position.total_quantity * sell_ratio)
        if sell_qty <= 0:
            return
        
        self.position.reduce_position(sell_qty)
        self._upsert_signal(conn, ts, "S", sell_qty, current_price, reason)
        
        logger.info(f"卖出执行: 数量={sell_qty}, 价格={current_price:.4f}, "
                   f"剩余仓位={self.position.total_quantity}, 原因={reason}")
    
    def run_strategy_loop(self, poll_sec: float = 1.0) -> None:
        """运行策略主循环"""
        try:
            conn = sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES)
        except Exception as e:
            logger.error(f"打开数据库失败: {e}")
            return
        
        self._ensure_signal_table(conn)
        logger.info(f"启动增强策略引擎: {self.symbol}")
        
        # 检查数据库中是否有数据
        try:
            cursor = conn.execute("SELECT COUNT(*) FROM ticks WHERE symbol=?", (self.symbol,))
            total_records = cursor.fetchone()[0]
            logger.info(f"数据库中 {self.symbol} 共有 {total_records} 条记录")
            
            # 检查是否需要收盘前平仓
            if (getattr(self, 'exit_at_close', False) and 
                self.position.total_quantity > 0):
                # 检查是否接近收盘时间（14:55以后）
                current_time = datetime.now()
                if (current_time.hour == 14 and current_time.minute >= 55) or current_time.hour == 15:
                    logger.info("收盘前平仓")
                    self.execute_sell(current_price, current_time, 1.0, "收盘前平仓")
                    return

            # 策略主循环
            loop_count = 0
            while self.is_running:
                loop_count += 1
                if loop_count % 100 == 0:
                    logger.info(f"策略运行中... 循环次数: {loop_count}, 当前仓位: {self.position.total_quantity}")
                
                # 获取最新tick数据
                latest_tick = self.get_latest_tick()
                if not latest_tick:
                    time.sleep(self.poll_sec)
                    continue
                
                current_price = float(latest_tick["price"])
                ts = latest_tick["time"].to_pydatetime()
                signal = self._safe_return_20ticks(latest_tick)
                
                # 输出当前状态
                if loop_count % 50 == 0:
                    logger.info(f"当前状态: 价格={current_price:.4f}, 信号={signal:.4f}, "
                              f"仓位={self.position.total_quantity}, 成本={self.position.get_profit_rate(current_price):.4f}")
                
                # 更新风险管理器
                market_value = self.position.total_quantity * current_price if self.position.total_quantity > 0 else 0.0
                current_equity = self.config.initial_capital - self.position.total_cost + market_value
                self.risk_manager.update_equity(current_equity)
                
                # 卖出判断
                # 优先处理卖出逻辑（避免同时买卖）
                should_sell, sell_ratio, sell_reason = self.should_sell(current_price)
                if should_sell:
                    self.execute_sell(conn, current_price, ts, sell_ratio, sell_reason)
                    time.sleep(1.0)  # 卖出后等待更长时间
                    continue
                
                # 买入判断（只有在没有卖出时才执行）
                if self.should_buy(signal, current_price):
                    # 买入前重置止盈标记，确保新买入的仓位可以享受完整的止盈策略
                    if self.position.total_quantity > 0:
                        # 如果已有仓位，重置部分止盈标记
                        self.position.reset_partial_sold_flags()
                    self.execute_buy(conn, current_price, ts)
                    time.sleep(1.0)
                    continue
                
                # 常规休眠
                time.sleep(poll_sec if poll_sec > 0 else 1.0)
                
        except Exception as e:
            logger.error(f"策略循环异常: {e}")
            time.sleep(1.0)


def _init_logger(level: int = logging.INFO) -> None:
    logging.basicConfig(level=level, format="%(asctime)s %(levelname)s %(message)s")


if __name__ == "__main__":
    import argparse
    _init_logger()
    
    parser = argparse.ArgumentParser(description="增强版策略引擎")
    parser.add_argument("--symbol", required=True, type=str, help="交易标的")
    parser.add_argument("--poll-sec", type=float, default=1.0, help="轮询间隔")
    parser.add_argument("--initial-capital", type=float, default=1_000_000.0, help="初始资金")
    
    args = parser.parse_args()
    
    strategy = EnhancedStrategy(args.symbol, args.initial_capital)
    strategy.run_strategy_loop(args.poll_sec)