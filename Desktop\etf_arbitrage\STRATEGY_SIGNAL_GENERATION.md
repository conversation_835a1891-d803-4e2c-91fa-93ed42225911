# 策略信号生成规则说明

## 1. 概述

本策略采用波动率作为主要交易信号，通过计算一段时间内价格的波动情况来判断市场活跃度，进而触发买入或卖出操作。

## 2. 信号计算方法

### 2.1 波动率计算

策略信号基于价格波动率计算，公式如下：

```
波动率 = (最高价 - 最新价) / 最新价
```

其中：
- 最高价：指定时间窗口内的最高价格
- 最新价：指定时间窗口内的最新价格

### 2.2 计算参数

- **时间窗口**：默认使用最近20个tick数据（可通过[signal_window](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L211-L211)参数调整）
- **计算逻辑**：取时间窗口内的最高价格与最新价格计算波动率

## 3. 信号解释

### 3.1 信号值含义

- **正值**：表示当前价格低于窗口期内的最高价，存在波动机会
- **负值**：理论上不会出现，因为最新价不会高于最高价
- **零值**：表示最新价就是窗口期内的最高价，无波动

### 3.2 信号强度判断

- **高波动率**（如 >= 0.006，即0.6%）：市场活跃，适合买入
- **低波动率**（如 <= 0.001，即0.1%）：市场平静，适合考虑卖出
- **中等波动率**：市场正常，维持当前持仓

## 4. 交易决策规则

### 4.1 买入条件

当满足以下所有条件时触发买入：

1. 波动率信号 <= [buy_trigger_drop](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L54-L54)（默认-0.006）
2. 当前无持仓或持仓未满
3. 通过风险管理检查（未触发日损失限制和最大回撤限制）
4. 当前价格有效（大于0）

### 4.2 卖出条件

当满足以下任一条件时触发卖出：

1. **止盈条件**：
   - 持仓收益率 >= [profit_target](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L55-L55)（默认0.006）
   - 分批止盈：根据多个止盈级别逐步卖出

2. **止损条件**：
   - 持仓收益率 <= [stop_loss](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L56-L56)（默认-0.02）

3. **时间止损**：
   - 持仓时间 >= [max_hold_time](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L82-L82)（默认3600秒，即1小时）

4. **收盘前平仓**：
   - 如果启用[exit_at_close](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L200-L200)参数且接近收盘时间（14:55后）

## 5. 分批交易机制

### 5.1 分层买入

采用分层买入策略，按比例分批建仓：

- 第一层：[layer1_ratio](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L142-L142)（默认40%）
- 第二层：[layer2_ratio](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L149-L149)（默认35%）
- 第三层：[layer3_ratio](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L156-L156)（默认25%）

### 5.2 分批止盈

设置多个止盈级别：

1. 第一止盈点：[profit_target](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L55-L55) * [partial_profit_multiplier1](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L109-L109)（默认0.006 * 0.5 = 0.003）
2. 第二止盈点：[profit_target](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L55-L55) * [partial_profit_multiplier2](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L116-L116)（默认0.006 * 1.0 = 0.006）
3. 第三止盈点：[profit_target](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L55-L55) * [partial_profit_multiplier3](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L123-L123)（默认0.006 * 2.0 = 0.012）

每个止盈点对应的卖出比例：
- 第一止盈：[partial_sell_ratio1](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L130-L130)（默认30%）
- 第二止盈：[partial_sell_ratio2](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L137-L137)（默认40%）
- 第三止盈：[partial_sell_ratio3](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L144-L144)（默认30%）

## 6. 风险管理

### 6.1 风险控制参数

- **日损失限制**：[daily_loss_limit](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L89-L89)（默认-5%）
- **最大回撤限制**：[max_drawdown_limit](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L96-L96)（默认-10%）
- **最大持仓时间**：[max_hold_time](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L82-L82)（默认3600秒）

### 6.2 仓位管理

- **最大持仓数量**：[max_position](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L74-L74)（默认100000）
- **单次买入仓位大小**：[position_size](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L81-L81)（默认100000）

## 7. 信号可视化

在实时监控和回测界面中，信号以不同颜色显示：

- **绿色**：波动率信号 <= [buy_trigger_drop](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L54-L54)，买入信号
- **红色**：波动率信号 >= [profit_target](file:///c:/Users/<USER>/Desktop/etf_arbitrage/strategy_config.py#L55-L55)，卖出信号
- **灰色**：中等波动率，维持当前状态

## 8. 参数配置

所有参数均可通过策略配置系统进行调整，用户可以根据不同市场环境和风险偏好进行个性化设置。