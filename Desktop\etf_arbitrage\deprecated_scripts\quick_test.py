try:
    import app_enhanced_realtime_dashboard
    from app_enhanced_realtime_dashboard import RealtimeSimulator
    from strategy_config import StrategyConfig
    
    # 测试模拟器创建
    simulator = RealtimeSimulator()
    
    # 测试配置
    config_dict = StrategyConfig.get_default_values()
    initial_position = {'quantity': 0, 'avg_cost': 0.0}
    simulator.initialize_strategy(config_dict, initial_position, 1000000.0)
    
    print('✅ 实时监控页面修复成功！')
    print('可以使用以下命令启动:')
    print('streamlit run app_enhanced_realtime_dashboard.py')
except Exception as e:
    print(f'❌ 修复失败: {e}')
    import traceback
    traceback.print_exc()