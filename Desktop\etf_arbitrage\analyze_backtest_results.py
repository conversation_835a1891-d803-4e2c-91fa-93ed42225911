#!/usr/bin/env python3
"""
分析回测结果
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_backtest_results():
    """分析回测结果"""
    
    # 读取CSV数据
    df = pd.read_csv('2025-09-01T02-54_export.csv')
    
    print("=== 回测结果分析报告 ===")
    print(f"回测期间: 2025-08-26 至 2025-08-28")
    print(f"总交易次数: {len(df)}笔")
    
    # 基本盈亏统计
    total_pnl = df['pnl'].sum()
    print(f"\n💰 总盈亏: {total_pnl:.2f}元")
    
    # 盈亏交易分析
    profit_trades = df[df['pnl'] > 0]
    loss_trades = df[df['pnl'] < 0]
    zero_trades = df[df['pnl'] == 0]
    
    print(f"\n📊 交易分布:")
    print(f"盈利交易: {len(profit_trades)}笔, 总盈利: {profit_trades['pnl'].sum():.2f}元")
    print(f"亏损交易: {len(loss_trades)}笔, 总亏损: {loss_trades['pnl'].sum():.2f}元")
    print(f"无盈亏交易: {len(zero_trades)}笔")
    
    # 胜率计算
    valid_trades = len(df[df['pnl'] != 0])
    if valid_trades > 0:
        win_rate = len(profit_trades) / valid_trades * 100
        print(f"胜率: {win_rate:.1f}%")
    
    # 最大盈亏
    if len(profit_trades) > 0:
        max_profit = profit_trades['pnl'].max()
        print(f"最大单笔盈利: {max_profit:.2f}元")
    
    if len(loss_trades) > 0:
        max_loss = loss_trades['pnl'].min()
        print(f"最大单笔亏损: {max_loss:.2f}元")
    
    # 交易原因分析
    print(f"\n🎯 交易原因分析:")
    reasons = df['reason'].value_counts()
    for reason, count in reasons.items():
        if pd.notna(reason):
            reason_pnl = df[df['reason'] == reason]['pnl'].sum()
            print(f"{reason}: {count}笔, 盈亏: {reason_pnl:.2f}元")
    
    # 按日期分析
    print(f"\n📅 按日期分析:")
    df['date'] = pd.to_datetime(df['time']).dt.date
    daily_pnl = df.groupby('date')['pnl'].sum()
    for date, pnl in daily_pnl.items():
        daily_trades = len(df[df['date'] == date])
        print(f"{date}: {daily_trades}笔交易, 盈亏: {pnl:.2f}元")
    
    # 交易时间分析
    print(f"\n⏰ 交易时间分析:")
    df['hour'] = pd.to_datetime(df['time']).dt.hour
    hourly_stats = df.groupby('hour').agg({
        'pnl': ['count', 'sum'],
        'quantity': 'sum'
    }).round(2)
    
    for hour in sorted(df['hour'].unique()):
        hour_data = df[df['hour'] == hour]
        trades_count = len(hour_data)
        hour_pnl = hour_data['pnl'].sum()
        print(f"{hour:02d}时: {trades_count}笔交易, 盈亏: {hour_pnl:.2f}元")
    
    # 买卖比例分析
    buy_trades = df[df['type'] == 'BUY']
    sell_trades = df[df['type'] == 'SELL']
    
    print(f"\n📈 买卖分析:")
    print(f"买入交易: {len(buy_trades)}笔")
    print(f"卖出交易: {len(sell_trades)}笔")
    print(f"买入总量: {buy_trades['quantity'].sum():,}股")
    print(f"卖出总量: {sell_trades['quantity'].sum():,}股")
    
    # 价格区间分析
    print(f"\n💹 价格区间分析:")
    print(f"最高交易价格: {df['price'].max():.3f}")
    print(f"最低交易价格: {df['price'].min():.3f}")
    print(f"平均交易价格: {df['price'].mean():.3f}")
    
    # 问题识别
    print(f"\n⚠️ 发现的问题:")
    
    # 1. 过度交易
    if len(df) > 100:
        print(f"1. 过度交易: 3天内{len(df)}笔交易，频率过高")
    
    # 2. 大额止损
    big_losses = loss_trades[loss_trades['pnl'] < -10000]
    if len(big_losses) > 0:
        print(f"2. 大额止损: {len(big_losses)}笔大额亏损，最大亏损{big_losses['pnl'].min():.2f}元")
    
    # 3. 收盘平仓
    close_trades = df[df['reason'].str.contains('收盘', na=False)]
    if len(close_trades) > 0:
        close_pnl = close_trades['pnl'].sum()
        print(f"3. 收盘平仓: {len(close_trades)}笔收盘平仓，盈亏: {close_pnl:.2f}元")
    
    # 4. 频繁买卖
    consecutive_same = 0
    for i in range(1, len(df)):
        if df.iloc[i]['type'] == df.iloc[i-1]['type']:
            consecutive_same += 1
    
    if consecutive_same > len(df) * 0.3:
        print(f"4. 交易逻辑异常: 连续同类型交易过多")

if __name__ == "__main__":
    analyze_backtest_results()
