#!/usr/bin/env python3
"""
测试Dashboard所需的所有参数
"""

from strategy_config import StrategyConfig

def test_dashboard_params():
    print("=== 测试Dashboard参数 ===")
    
    # Dashboard中使用的所有参数
    required_params = [
        'initial_capital',
        'buy_trigger_drop', 
        'profit_target',
        'stop_loss',
        'max_holding_days',
        'commission_rate',
        'slippage',
        'ma_short',
        'position_size'
    ]
    
    success_count = 0
    total_count = len(required_params)
    
    for param in required_params:
        try:
            config = StrategyConfig.get_streamlit_config(param)
            print(f"✅ {param}: {config['value']} (范围: {config['min_value']}-{config['max_value']})")
            success_count += 1
        except Exception as e:
            print(f"❌ {param}: 错误 - {e}")
    
    print(f"\n结果: {success_count}/{total_count} 参数测试通过")
    
    if success_count == total_count:
        print("🎉 所有Dashboard参数都已正确配置！")
        return True
    else:
        print("❌ 还有参数需要修复")
        return False

if __name__ == "__main__":
    test_dashboard_params()