#!/usr/bin/env python3
"""
测试简化分批止盈逻辑
验证新的实现是否正确工作
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from datetime import datetime
from strategy_engine_enhanced import Position
from simplified_profit_taking import SimplifiedProfitTaking, SimplifiedProfitConfig

def test_original_logic():
    """测试原始复杂逻辑"""
    print("=== 测试原始复杂分批止盈逻辑 ===")
    
    # 创建持仓
    position = Position()
    position.add_position(10000, 10.0, datetime.now())
    
    # 测试不同价格下的止盈
    test_prices = [10.03, 10.06, 10.12, 10.15]
    
    for price in test_prices:
        profit_rate = position.get_profit_rate(price)
        should_sell, level_idx, sell_ratio, reason = position.check_partial_sell(price)
        
        print(f"价格: {price:.2f}, 收益率: {profit_rate:.2%}")
        print(f"是否卖出: {should_sell}, 卖出比例: {sell_ratio:.1%}, 原因: {reason}")
        print(f"当前止盈级别: {position.profit_level_reached}")
        print("-" * 50)

def test_simplified_logic():
    """测试简化逻辑"""
    print("\n=== 测试简化分批止盈逻辑 ===")
    
    # 创建简化止盈管理器
    config = SimplifiedProfitConfig(
        level1_threshold=0.003,  # 0.3%
        level2_threshold=0.006,  # 0.6%
        level3_threshold=0.012,  # 1.2%
        level1_sell_ratio=0.3,   # 30%
        level2_sell_ratio=0.5,   # 50%
        level3_sell_ratio=0.7    # 70%
    )
    
    profit_manager = SimplifiedProfitTaking(config)
    
    # 测试不同收益率
    test_profit_rates = [0.002, 0.004, 0.007, 0.015]
    
    for profit_rate in test_profit_rates:
        should_sell, sell_ratio, reason = profit_manager.check_profit_taking(profit_rate)
        status = profit_manager.get_status()
        
        print(f"收益率: {profit_rate:.2%}")
        print(f"是否卖出: {should_sell}, 卖出比例: {sell_ratio:.1%}, 原因: {reason}")
        print(f"当前级别: {status['current_level']}/3, 下一阈值: {status['next_threshold']:.2%}")
        print("-" * 50)

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    profit_manager = SimplifiedProfitTaking()
    
    # 测试重复触发同一级别
    print("1. 测试重复触发防护")
    profit_rate = 0.004  # 0.4%，应该触发第一级
    
    for i in range(3):
        should_sell, sell_ratio, reason = profit_manager.check_profit_taking(profit_rate)
        print(f"第{i+1}次检查: 是否卖出={should_sell}, 比例={sell_ratio:.1%}")
    
    print("\n2. 测试跳级触发")
    profit_manager.reset()
    profit_rate = 0.015  # 1.5%，应该直接触发第三级
    
    should_sell, sell_ratio, reason = profit_manager.check_profit_taking(profit_rate)
    status = profit_manager.get_status()
    print(f"跳级测试: 收益率={profit_rate:.2%}, 触发级别={status['current_level']}")
    print(f"卖出比例: {sell_ratio:.1%}, 原因: {reason}")

def compare_performance():
    """性能对比测试"""
    print("\n=== 性能对比测试 ===")
    
    import time
    
    # 测试原始逻辑性能
    position = Position()
    position.add_position(10000, 10.0, datetime.now())
    
    start_time = time.time()
    for _ in range(10000):
        position.check_partial_sell(10.06)
    original_time = time.time() - start_time
    
    # 测试简化逻辑性能
    profit_manager = SimplifiedProfitTaking()
    
    start_time = time.time()
    for _ in range(10000):
        profit_manager.check_profit_taking(0.006)
    simplified_time = time.time() - start_time
    
    print(f"原始逻辑耗时: {original_time:.4f}秒")
    print(f"简化逻辑耗时: {simplified_time:.4f}秒")
    print(f"性能提升: {(original_time/simplified_time):.2f}倍")

def test_integration():
    """集成测试"""
    print("\n=== 集成测试 ===")
    
    # 模拟完整交易流程
    position = Position()
    
    # 分批买入
    buy_prices = [10.0, 9.95, 9.90]
    quantities = [3000, 3000, 4000]
    
    for price, qty in zip(buy_prices, quantities):
        position.add_position(qty, price, datetime.now())
        print(f"买入: {qty}股 @ {price:.2f}, 总仓位: {position.total_quantity}")
    
    print(f"平均成本: {position.total_cost/position.total_quantity:.4f}")
    
    # 模拟价格上涨和分批卖出
    sell_prices = [10.03, 10.06, 10.12]
    
    for price in sell_prices:
        profit_rate = position.get_profit_rate(price)
        should_sell, level_idx, sell_ratio, reason = position.check_partial_sell(price)
        
        print(f"\n价格: {price:.2f}, 收益率: {profit_rate:.2%}")
        
        if should_sell:
            sell_qty = int(position.total_quantity * sell_ratio)
            print(f"执行卖出: {sell_qty}股 ({sell_ratio:.1%}), 原因: {reason}")
            
            # 这里应该调用position.reduce_position(sell_qty)
            # 但为了测试简化，我们只是显示
            remaining_qty = position.total_quantity - sell_qty
            print(f"剩余仓位: {remaining_qty}股")
        else:
            print("不卖出")

if __name__ == "__main__":
    test_original_logic()
    test_simplified_logic()
    test_edge_cases()
    compare_performance()
    test_integration()
