#!/usr/bin/env python3
"""
简化版实时交易模拟仪表板
基于增强版策略，支持自定义持仓情况
优化布局，一屏显示所有模块
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import sqlite3
import time
from typing import Dict, List, Optional

# 页面配置
st.set_page_config(
    page_title="增强版实时交易模拟",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 0.8rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin-bottom: 0.5rem;
    }
    .metric-card h4 {
        margin: 0 0 0.3rem 0;
        font-size: 0.9rem;
        color: #666;
    }
    .metric-card h2 {
        margin: 0;
        font-size: 1.5rem;
    }
    .positive {
        color: #00cc00;
        font-weight: bold;
    }
    .negative {
        color: #ff0000;
        font-weight: bold;
    }
    .neutral {
        color: #666666;
    }
    .trading-status {
        padding: 0.5rem;
        border-radius: 0.3rem;
        text-align: center;
        font-weight: bold;
        margin: 0.5rem 0;
    }
    .status-running {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .status-stopped {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .compact-metric {
        text-align: center;
        padding: 0.3rem;
        margin: 0.2rem 0;
    }
    .compact-metric .metric-label {
        font-size: 0.8rem;
        color: #666;
        margin: 0;
    }
    .compact-metric .metric-value {
        font-size: 1.1rem;
        font-weight: bold;
        margin: 0;
    }
</style>
""", unsafe_allow_html=True)

class SimpleRealtimeSimulator:
    """简化版实时交易模拟器"""
    
    def __init__(self):
        self.is_running = False
        self.config = {}
        self.position = {'quantity': 0, 'avg_cost': 0.0, 'total_cost': 0.0}
        self.initial_capital = 1000000
        self.current_equity = 1000000
        self.trade_history = []
        self.price_history = []
        self.signal_history = []
        self.equity_history = []
        
    def initialize(self, config: Dict, initial_position: Dict, initial_capital: float):
        """初始化模拟器"""
        self.config = config
        self.initial_capital = initial_capital
        self.current_equity = initial_capital
        
        # 设置初始持仓
        self.position = {
            'quantity': initial_position.get('quantity', 0),
            'avg_cost': initial_position.get('avg_cost', 0.0),
            'total_cost': initial_position.get('quantity', 0) * initial_position.get('avg_cost', 0.0)
        }
        
        # 调整初始净值
        if self.position['quantity'] > 0:
            self.current_equity = initial_capital
    
    def calculate_signal(self, prices: List[float]) -> float:
        """计算交易信号"""
        if len(prices) < 20:
            return 0.0
        
        # 获取最近20个tick价格
        recent_prices = prices[-20:]
        if len(recent_prices) < 2:
            return 0.0
        
        # 计算相对于20个tick中最高价的跌幅
        max_price = max(recent_prices)
        current_price = recent_prices[-1]
        
        # 信号 = (当前价格 - 最高价) / 最高价
        signal = (current_price - max_price) / max_price
        return signal
    
    def process_tick(self, tick_data: Dict, price_history: List[float]) -> Dict:
        """处理单个tick数据"""
        current_price = tick_data['price']
        current_time = tick_data['time']
        
        # 计算信号
        signal = self.calculate_signal(price_history)
        
        # 交易逻辑
        action = {'action': 'HOLD', 'quantity': 0, 'reason': ''}
        
        # 买入逻辑
        if (self.position['quantity'] == 0 and 
            signal <= self.config.get('buy_trigger_drop', -0.006)):
            
            buy_quantity = min(1000000, self.current_equity // current_price)
            if buy_quantity > 0:
                cost = buy_quantity * current_price
                self.position['quantity'] = buy_quantity
                self.position['avg_cost'] = current_price
                self.position['total_cost'] = cost
                self.current_equity -= cost
                
                action = {
                    'action': 'BUY',
                    'quantity': buy_quantity,
                    'reason': f'信号触发: {signal:.6f}'
                }
        
        # 卖出逻辑
        elif self.position['quantity'] > 0:
            profit_rate = (current_price - self.position['avg_cost']) / self.position['avg_cost']
            
            # 止盈
            if profit_rate >= self.config.get('profit_target', 0.006):
                sell_quantity = self.position['quantity']
                proceeds = sell_quantity * current_price
                self.current_equity += proceeds
                
                action = {
                    'action': 'SELL',
                    'quantity': sell_quantity,
                    'reason': f'止盈: {profit_rate:.4f}'
                }
                
                self.position = {'quantity': 0, 'avg_cost': 0.0, 'total_cost': 0.0}
            
            # 止损
            elif profit_rate <= self.config.get('stop_loss', -0.02):
                sell_quantity = self.position['quantity']
                proceeds = sell_quantity * current_price
                self.current_equity += proceeds
                
                action = {
                    'action': 'SELL',
                    'quantity': sell_quantity,
                    'reason': f'止损: {profit_rate:.4f}'
                }
                
                self.position = {'quantity': 0, 'avg_cost': 0.0, 'total_cost': 0.0}
        
        # 计算当前净值
        position_value = self.position['quantity'] * current_price
        total_equity = self.current_equity + position_value
        
        # 记录历史数据
        self.signal_history.append({
            'time': current_time,
            'signal': signal,
            'position': self.position['quantity']
        })
        
        self.equity_history.append({
            'time': current_time,
            'equity': total_equity,
            'position_value': position_value,
            'cash': self.current_equity
        })
        
        # 记录交易
        if action['action'] != 'HOLD':
            trade_record = {
                'time': current_time,
                'action': action['action'],
                'quantity': action['quantity'],
                'price': current_price,
                'reason': action['reason'],
                'position_after': self.position['quantity']
            }
            self.trade_history.append(trade_record)
        
        return {
            'signal': signal,
            'action': action,
            'position': self.position['quantity'],
            'equity': total_equity,
            'pnl': total_equity - self.initial_capital
        }

@st.cache_data
def load_available_symbols():
    """加载可用的交易标的"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute("SELECT DISTINCT symbol FROM ticks ORDER BY symbol")
        symbols = [row[0] for row in cursor.fetchall()]
        conn.close()
        return symbols
    except:
        return ["159740"]

def get_latest_tick(symbol: str) -> Optional[Dict]:
    """获取最新tick数据"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute("""
            SELECT tick_time, price, volume 
            FROM ticks 
            WHERE symbol=? 
            ORDER BY tick_time DESC 
            LIMIT 1
        """, (symbol,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'time': result[0],
                'price': float(result[1]),
                'volume': int(result[2])
            }
        return None
    except Exception as e:
        st.error(f"获取数据失败: {e}")
        return None

def get_recent_prices(symbol: str, count: int = 50) -> List[float]:
    """获取最近的价格数据"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute("""
            SELECT price 
            FROM ticks 
            WHERE symbol=? 
            ORDER BY tick_time DESC 
            LIMIT ?
        """, (symbol, count))
        
        results = cursor.fetchall()
        conn.close()
        
        prices = [float(row[0]) for row in reversed(results)]
        return prices
    except:
        return []

def create_realtime_chart(simulator: SimpleRealtimeSimulator, lookback_minutes: int = 30) -> go.Figure:
    """创建实时图表"""
    if not simulator.price_history:
        fig = go.Figure()
        fig.add_annotation(
            text="等待数据...",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(height=300)
        return fig
    
    # 获取最近的数据 - 简化时间处理
    recent_count = min(100, len(simulator.price_history))  # 最近100个数据点
    
    price_data = simulator.price_history[-recent_count:] if simulator.price_history else []
    signal_data = simulator.signal_history[-recent_count:] if simulator.signal_history else []
    trade_data = simulator.trade_history[-20:] if simulator.trade_history else []  # 最近20笔交易
    
    if not price_data:
        fig = go.Figure()
        fig.add_annotation(text="暂无数据", x=0.5, y=0.5, showarrow=False)
        fig.update_layout(height=300)
        return fig
    
    # 单行图表，价格和信号合并显示
    fig = go.Figure()
    
    # 价格线
    times = [p['time'] for p in price_data]
    prices = [p['price'] for p in price_data]
    
    fig.add_trace(
        go.Scatter(
            x=times, y=prices,
            mode='lines',
            name='价格',
            line=dict(color='blue', width=2),
            hovertemplate='时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
        )
    )
    
    # 交易点位
    if trade_data:
        buy_trades = [t for t in trade_data if t['action'] == 'BUY']
        sell_trades = [t for t in trade_data if t['action'] == 'SELL']
        
        if buy_trades:
            fig.add_trace(
                go.Scatter(
                    x=[t['time'] for t in buy_trades],
                    y=[t['price'] for t in buy_trades],
                    mode='markers',
                    name='买入',
                    marker=dict(symbol='triangle-up', size=12, color='green'),
                    hovertemplate='买入<br>时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
                )
            )
        
        if sell_trades:
            fig.add_trace(
                go.Scatter(
                    x=[t['time'] for t in sell_trades],
                    y=[t['price'] for t in sell_trades],
                    mode='markers',
                    name='卖出',
                    marker=dict(symbol='triangle-down', size=12, color='red'),
                    hovertemplate='卖出<br>时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
                )
            )
    
    # 更新布局
    fig.update_layout(
        title="实时价格与交易",
        height=300,
        showlegend=True,
        hovermode='x unified',
        margin=dict(l=40, r=40, t=40, b=40)
    )
    
    fig.update_yaxes(title_text="价格")
    fig.update_xaxes(title_text="时间")
    
    return fig

def create_position_chart(simulator: SimpleRealtimeSimulator) -> go.Figure:
    """创建持仓变化图表"""
    if not simulator.signal_history:
        fig = go.Figure()
        fig.add_annotation(
            text="等待数据...",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(height=250)
        return fig
    
    # 获取最近的数据
    recent_count = min(100, len(simulator.signal_history))
    signal_data = simulator.signal_history[-recent_count:] if simulator.signal_history else []
    
    if not signal_data:
        fig = go.Figure()
        fig.add_annotation(text="暂无数据", x=0.5, y=0.5, showarrow=False)
        fig.update_layout(height=250)
        return fig
    
    times = [s['time'] for s in signal_data]
    positions = [s['position'] for s in signal_data]
    
    fig = go.Figure()
    
    # 持仓量线
    fig.add_trace(
        go.Scatter(
            x=times, y=positions,
            mode='lines+markers',
            name='持仓数量',
            line=dict(color='orange', width=2),
            marker=dict(size=4),
            fill='tonexty',
            fillcolor='rgba(255,165,0,0.1)',
            hovertemplate='时间: %{x}<br>持仓: %{y:,}<extra></extra>'
        )
    )
    
    # 添加零线
    fig.add_hline(y=0, line_dash="dash", line_color="gray", opacity=0.5)
    
    fig.update_layout(
        height=200,
        showlegend=False,
        hovermode='x unified',
        margin=dict(l=40, r=40, t=10, b=10)
    )
    
    fig.update_yaxes(title_text="持仓数量")
    fig.update_xaxes(title_text="时间")
    
    return fig

def create_signal_strength_chart(simulator: SimpleRealtimeSimulator, current_signal: float = 0.0) -> go.Figure:
    """创建信号强度图表"""
    if not simulator.signal_history:
        fig = go.Figure()
        fig.add_annotation(
            text="等待数据...",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(height=250)
        return fig
    
    # 获取最近的数据
    recent_count = min(100, len(simulator.signal_history))
    signal_data = simulator.signal_history[-recent_count:] if simulator.signal_history else []
    
    if not signal_data:
        fig = go.Figure()
        fig.add_annotation(text="暂无数据", x=0.5, y=0.5, showarrow=False)
        fig.update_layout(height=250)
        return fig
    
    times = [s['time'] for s in signal_data]
    # 重新计算所有信号值，确保与当前策略一致
    times = []
    signals = []
    
    # 获取对应的价格历史数据
    recent_price_count = min(len(simulator.price_history), len(signal_data) + 19)  # 需要额外19个点来计算信号
    recent_prices = simulator.price_history[-recent_price_count:] if simulator.price_history else []
    
    if len(recent_prices) >= 20:
        # 为每个时间点重新计算信号
        for i in range(19, len(recent_prices)):  # 从第20个点开始计算
            if i - 19 < len(signal_data):
                time_point = signal_data[i - 19]['time']
                price_window = [p['price'] for p in recent_prices[i-19:i+1]]  # 20个价格点
                
                # 使用当前策略重新计算信号
                if len(price_window) >= 20:
                    max_price = max(price_window)
                    current_price_point = price_window[-1]
                    recalc_signal = (current_price_point - max_price) / max_price
                    
                    times.append(time_point)
                    signals.append(recalc_signal)
    
    # 如果没有足够数据，回退到原始方法
    if not signals:
        times = [s['time'] for s in signal_data]
        signals = [s['signal'] for s in signal_data]
    
    # 获取策略参数
    buy_trigger = simulator.config.get('buy_trigger_drop', -0.006)
    profit_target = simulator.config.get('profit_target', 0.006)
    
    fig = go.Figure()
    
    # 信号强度柱状图 - 使用参数化阈值
    colors = ['red' if s <= buy_trigger else 'green' if s >= profit_target else 'gray' for s in signals]
    
    fig.add_trace(
        go.Bar(
            x=times, y=signals,
            name='信号强度',
            marker_color=colors,
            hovertemplate='时间: %{x}<br>信号: %{y:.6f}<extra></extra>'
        )
    )
    
    # 添加参数化的买入/卖出阈值线
    fig.add_hline(y=buy_trigger, line_dash="dash", line_color="red", opacity=0.7, 
                  annotation_text=f"买入阈值: {buy_trigger:.3f}")
    fig.add_hline(y=profit_target, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text=f"卖出阈值: {profit_target:.3f}")
    fig.add_hline(y=0, line_dash="solid", line_color="gray", opacity=0.3)
    
    fig.update_layout(
        height=200,
        showlegend=False,
        hovermode='x unified',
        margin=dict(l=40, r=40, t=10, b=10)
    )
    
    fig.update_yaxes(title_text="信号强度")
    fig.update_xaxes(title_text="时间")
    
    return fig

def create_equity_chart(simulator: SimpleRealtimeSimulator, initial_capital: float) -> go.Figure:
    """创建净值变化图表"""
    if not simulator.equity_history:
        fig = go.Figure()
        fig.add_annotation(
            text="等待数据...",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(height=300)
        return fig
    
    # 获取最近的数据
    recent_count = min(100, len(simulator.equity_history))
    equity_data = simulator.equity_history[-recent_count:] if simulator.equity_history else []
    
    if not equity_data:
        fig = go.Figure()
        fig.add_annotation(text="暂无数据", x=0.5, y=0.5, showarrow=False)
        fig.update_layout(height=300)
        return fig
    
    times = [e['time'] for e in equity_data]
    equities = [e['equity'] for e in equity_data]
    cash_values = [e['cash'] for e in equity_data]
    position_values = [e['position_value'] for e in equity_data]
    
    fig = go.Figure()
    
    # 总净值线
    fig.add_trace(
        go.Scatter(
            x=times, y=equities,
            mode='lines',
            name='总净值',
            line=dict(color='blue', width=3),
            hovertemplate='时间: %{x}<br>总净值: %{y:,.2f}<extra></extra>'
        )
    )
    
    # 现金价值线
    fig.add_trace(
        go.Scatter(
            x=times, y=cash_values,
            mode='lines',
            name='现金',
            line=dict(color='green', width=2, dash='dash'),
            hovertemplate='时间: %{x}<br>现金: %{y:,.2f}<extra></extra>'
        )
    )
    
    # 持仓价值线
    fig.add_trace(
        go.Scatter(
            x=times, y=position_values,
            mode='lines',
            name='持仓价值',
            line=dict(color='orange', width=2, dash='dot'),
            hovertemplate='时间: %{x}<br>持仓价值: %{y:,.2f}<extra></extra>'
        )
    )
    
    # 添加初始资金基准线
    fig.add_hline(y=initial_capital, line_dash="solid", line_color="gray", opacity=0.5,
                  annotation_text=f"初始资金: {initial_capital:,.0f}")
    
    # 计算盈亏并添加填充
    if equities:
        profit_loss = [e - initial_capital for e in equities]
        colors = ['rgba(0,255,0,0.1)' if pl >= 0 else 'rgba(255,0,0,0.1)' for pl in profit_loss]
        
        # 添加盈亏填充区域
        for i in range(len(times)):
            if i == 0:
                continue
            color = 'rgba(0,255,0,0.1)' if profit_loss[i] >= 0 else 'rgba(255,0,0,0.1)'
            fig.add_trace(
                go.Scatter(
                    x=[times[i-1], times[i]],
                    y=[initial_capital, initial_capital],
                    fill='tonexty',
                    fillcolor=color,
                    mode='none',
                    showlegend=False,
                    hoverinfo='skip'
                )
            )
    
    fig.update_layout(
        title="净值变化曲线",
        height=300,
        showlegend=True,
        hovermode='x unified',
        margin=dict(l=40, r=40, t=40, b=40),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    fig.update_yaxes(title_text="金额 (元)")
    fig.update_xaxes(title_text="时间")
    
    return fig

def main():
    """主函数"""
    st.markdown('<h1 class="main-header">🚀 增强版实时交易模拟</h1>', unsafe_allow_html=True)
    
    # 初始化session state
    if 'simulator' not in st.session_state:
        st.session_state.simulator = SimpleRealtimeSimulator()
    
    simulator = st.session_state.simulator
    
    # 侧边栏配置
    st.sidebar.header("🎛️ 交易配置")
    
    # 基本设置
    symbols = load_available_symbols()
    symbol = st.sidebar.selectbox("交易标的", symbols, index=0)
    
    initial_capital = st.sidebar.number_input(
        "初始资金", 
        min_value=100000, 
        max_value=10000000, 
        value=1000000, 
        step=100000
    )
    
    # 策略参数
    st.sidebar.subheader("📊 策略参数")
    
    buy_trigger = st.sidebar.slider(
        "买入触发跌幅", 
        min_value=-0.02, 
        max_value=-0.001, 
        value=-0.006, 
        step=0.001,
        format="%.3f"
    )
    
    profit_target = st.sidebar.slider(
        "止盈目标", 
        min_value=0.001, 
        max_value=0.02, 
        value=0.006, 
        step=0.001,
        format="%.3f"
    )
    
    stop_loss = st.sidebar.slider(
        "止损线", 
        min_value=-0.05, 
        max_value=-0.005, 
        value=-0.02, 
        step=0.001,
        format="%.3f"
    )
    
    # 初始持仓设置
    st.sidebar.subheader("💼 初始持仓")
    
    initial_position_qty = st.sidebar.number_input(
        "初始持仓数量", 
        min_value=0, 
        max_value=2000000, 
        value=0, 
        step=10000
    )
    
    initial_avg_cost = st.sidebar.number_input(
        "初始持仓成本", 
        min_value=0.000, 
        max_value=10.000, 
        value=0.000, 
        step=0.001,
        format="%.3f"
    )
    
    # 控制按钮
    st.sidebar.subheader("🎮 交易控制")
    
    col1, col2 = st.sidebar.columns(2)
    
    with col1:
        if st.button("🚀 启动", type="primary"):
            # 创建策略配置
            config = {
                'buy_trigger_drop': buy_trigger,
                'profit_target': profit_target,
                'stop_loss': stop_loss
            }
            
            # 初始持仓配置
            initial_position = {
                'quantity': initial_position_qty,
                'avg_cost': initial_avg_cost
            }
            
            # 初始化策略
            simulator.initialize(config, initial_position, initial_capital)
            simulator.is_running = True
            
            st.success("交易模拟已启动！")
    
    with col2:
        if st.button("⏹️ 停止"):
            simulator.is_running = False
            st.info("交易模拟已停止")
    
    if st.sidebar.button("🔄 重置"):
        st.session_state.simulator = SimpleRealtimeSimulator()
        st.rerun()
    
    # 手动交易功能
    st.sidebar.subheader("🤝 手动交易")
    
    # 获取当前价格用于手动交易
    current_tick = get_latest_tick(symbol)
    current_price = current_tick['price'] if current_tick else 0.0
    
    if current_price > 0:
        st.sidebar.write(f"当前价格: {current_price:.4f}")
        
        # 手动买入
        manual_buy_qty = st.sidebar.number_input(
            "买入数量", 
            min_value=0, 
            max_value=1000000, 
            value=10000, 
            step=1000,
            key="manual_buy_qty"
        )
        
        col3, col4 = st.sidebar.columns(2)
        
        with col3:
            if st.button("📈 买入", key="manual_buy"):
                if manual_buy_qty > 0 and simulator.current_equity >= manual_buy_qty * current_price:
                    # 执行手动买入
                    cost = manual_buy_qty * current_price
                    
                    if simulator.position['quantity'] > 0:
                        # 更新平均成本
                        total_cost = simulator.position['total_cost'] + cost
                        total_qty = simulator.position['quantity'] + manual_buy_qty
                        new_avg_cost = total_cost / total_qty
                        
                        simulator.position['quantity'] = total_qty
                        simulator.position['avg_cost'] = new_avg_cost
                        simulator.position['total_cost'] = total_cost
                    else:
                        # 新建仓位
                        simulator.position['quantity'] = manual_buy_qty
                        simulator.position['avg_cost'] = current_price
                        simulator.position['total_cost'] = cost
                    
                    simulator.current_equity -= cost
                    
                    # 记录交易
                    trade_record = {
                        'time': current_tick['time'],
                        'action': 'BUY',
                        'quantity': manual_buy_qty,
                        'price': current_price,
                        'reason': '手动买入',
                        'position_after': simulator.position['quantity']
                    }
                    simulator.trade_history.append(trade_record)
                    
                    st.success(f"手动买入 {manual_buy_qty:,} 股，价格 {current_price:.4f}")
                else:
                    st.error("资金不足或数量无效")
        
        with col4:
            if st.button("📉 卖出", key="manual_sell"):
                if simulator.position['quantity'] > 0:
                    # 卖出全部持仓
                    sell_qty = simulator.position['quantity']
                    proceeds = sell_qty * current_price
                    
                    simulator.current_equity += proceeds
                    
                    # 记录交易
                    trade_record = {
                        'time': current_tick['time'],
                        'action': 'SELL',
                        'quantity': sell_qty,
                        'price': current_price,
                        'reason': '手动卖出',
                        'position_after': 0
                    }
                    simulator.trade_history.append(trade_record)
                    
                    # 清空持仓
                    simulator.position = {'quantity': 0, 'avg_cost': 0.0, 'total_cost': 0.0}
                    
                    st.success(f"手动卖出 {sell_qty:,} 股，价格 {current_price:.4f}")
                else:
                    st.error("当前无持仓")
    
    # 主界面
    # 状态显示
    if simulator.is_running:
        status_class = "status-running"
        status_text = "🟢 运行中"
    else:
        status_class = "status-stopped"
        status_text = "🔴 已停止"
    
    st.markdown(f'<div class="trading-status {status_class}">{status_text}</div>', 
                unsafe_allow_html=True)
    
    # 实时数据处理
    if simulator.is_running:
        # 获取最新数据
        latest_tick = get_latest_tick(symbol)
        recent_prices = get_recent_prices(symbol, 50)
        
        if latest_tick and recent_prices:
            # 记录价格历史
            simulator.price_history.append({
                'time': latest_tick['time'],
                'price': latest_tick['price'],
                'volume': latest_tick['volume']
            })
            
            # 处理tick数据
            result = simulator.process_tick(latest_tick, recent_prices)
            
            # 显示当前状态 - 5列布局
            col1, col2, col3, col4, col5 = st.columns(5)
            
            with col1:
                current_price = latest_tick['price']
                st.markdown(f"""
                <div class="metric-card">
                    <h4>当前价格</h4>
                    <h2 class="neutral">{current_price:.4f}</h2>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                current_position = result.get('position', 0)
                position_value = current_position * current_price
                st.markdown(f"""
                <div class="metric-card">
                    <h4>当前持仓</h4>
                    <h2 class="neutral">{current_position:,}</h2>
                    <p>市值: {position_value:,.2f}</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col3:
                # 总市值指标
                total_market_value = simulator.current_equity + position_value
                st.markdown(f"""
                <div class="metric-card">
                    <h4>总市值</h4>
                    <h2 class="neutral">{total_market_value:,.2f}</h2>
                    <p>现金: {simulator.current_equity:,.2f}</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col4:
                current_pnl = result.get('pnl', 0)
                pnl_class = "positive" if current_pnl > 0 else "negative" if current_pnl < 0 else "neutral"
                pnl_pct = (current_pnl / initial_capital) * 100 if initial_capital > 0 else 0
                st.markdown(f"""
                <div class="metric-card">
                    <h4>浮动盈亏</h4>
                    <h2 class="{pnl_class}">{current_pnl:,.2f}</h2>
                    <p>({pnl_pct:+.2f}%)</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col5:
                current_signal = result.get('signal', 0)
                signal_class = "negative" if current_signal <= -0.006 else "positive" if current_signal >= 0.006 else "neutral"
                
                # 获取策略状态
                action_info = result.get('action', {})
                action_text = action_info.get('action', 'HOLD')
                action_reason = action_info.get('reason', '')
                
                st.markdown(f"""
                <div class="metric-card">
                    <h4>交易信号</h4>
                    <h2 class="{signal_class}">{current_signal:.6f}</h2>
                    <p>动作: {action_text}</p>
                    <p style="font-size:0.8em;">{action_reason}</p>
                </div>
                """, unsafe_allow_html=True)
            
            # 图表显示 - 三图布局
            # 策略状态显示
            st.subheader("🎯 策略状态监控")
            
            col_a, col_b, col_c = st.columns(3)
            
            with col_a:
                buy_trigger = simulator.config.get('buy_trigger_drop', -0.006)
                trigger_status = "🟢 已触发" if current_signal <= buy_trigger else "⚪ 未触发"
                st.markdown(f"""
                # 详细的买入条件检查
                has_position = current_position > 0
                has_cash = simulator.current_equity >= current_price * 1000  # 至少能买1000股
                signal_triggered = current_signal <= buy_trigger
                """)
                st.markdown(
                """
                **买入条件检查**  
                信号阈值: {buy_trigger:.6f}  
                当前信号: {current_signal:.6f}  
                信号触发: {'✅' if signal_triggered else '❌'}  
                无持仓: {'✅' if not has_position else '❌'}  
                资金充足: {'✅' if has_cash else '❌'}  
                状态: {trigger_status}
                """)
            
            with col_b:
                if current_position > 0:
                    profit_target = simulator.config.get('profit_target', 0.006)
                    stop_loss = simulator.config.get('stop_loss', -0.02)
                    current_profit_rate = (current_price - simulator.position['avg_cost']) / simulator.position['avg_cost']
                    
                    sell_status = "🟢 止盈触发" if current_profit_rate >= profit_target else "🔴 止损触发" if current_profit_rate <= stop_loss else "⚪ 持仓中"
                    
                    st.markdown(f"""
                    **卖出条件**  
                    止盈线: {profit_target:.4f}  
                    止损线: {stop_loss:.4f}  
                    当前盈亏: {current_profit_rate:.4f}  
                    状态: {sell_status}
                    """)
                else:
                    st.markdown("**卖出条件**  \n无持仓")
            
            with col_c:
                available_cash = simulator.current_equity
                max_buy_qty = available_cash // current_price if current_price > 0 else 0
                
                st.markdown(f"""
                **资金状态**  
                可用资金: {available_cash:,.0f}  
                最大买入: {max_buy_qty:,} 股  
                买入金额: {max_buy_qty * current_price:,.0f}
                """)
            
            # 图表显示 - 三图布局
            # 图表显示 - 调整布局顺序：主图、下图、中图
            st.subheader("📊 实时交易监控")
            
            # 主图：价格和交易信号
            fig_main = create_realtime_chart(simulator, 30)
            st.plotly_chart(fig_main, use_container_width=True)
            
            # 下图：信号强度图（紧贴主图，无标题）
            # 下图：信号强度图（紧贴下图，无标题）
            fig_signal = create_signal_strength_chart(simulator, current_signal)
            st.plotly_chart(fig_signal, use_container_width=True)
            
            # 中图：持仓变化图（紧贴下图，无标题）
            fig_position = create_position_chart(simulator)
            st.plotly_chart(fig_position, use_container_width=True)
            
            # 净值变化图
            st.subheader("💰 净值变化图")
            fig_equity = create_equity_chart(simulator, initial_capital)
            st.plotly_chart(fig_equity, use_container_width=True)
            
            # 交易明细
            if simulator.trade_history:
                st.subheader("📋 交易明细")
                
                # 创建两列布局
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    # 交易记录表格
                    trades_df = pd.DataFrame(simulator.trade_history)
                    
                    # 添加盈亏计算
                    trades_df['累计盈亏'] = 0.0
                    trades_df['单笔盈亏'] = 0.0
                    
                    running_pnl = 0.0
                    for i, row in trades_df.iterrows():
                        if row['action'] == 'SELL':
                            # 计算单笔盈亏
                            if i > 0:
                                # 找到对应的买入记录
                                buy_records = trades_df[trades_df['action'] == 'BUY'].iloc[:i+1]
                                if not buy_records.empty:
                                    avg_buy_price = (buy_records['price'] * buy_records['quantity']).sum() / buy_records['quantity'].sum()
                                    single_pnl = (row['price'] - avg_buy_price) * row['quantity']
                                    trades_df.at[i, '单笔盈亏'] = single_pnl
                                    running_pnl += single_pnl
                        
                        trades_df.at[i, '累计盈亏'] = running_pnl
                    
                    # 格式化显示
                    display_df = trades_df[['time', 'action', 'quantity', 'price', '单笔盈亏', '累计盈亏', 'reason']].copy()
                    display_df.columns = ['时间', '操作', '数量', '价格', '单笔盈亏', '累计盈亏', '原因']
                    
                    # 只显示最近20笔交易
                    recent_trades = display_df.tail(20)
                    
                    st.dataframe(
                        recent_trades.style.format({
                            '价格': '{:.4f}',
                            '数量': '{:,}',
                            '单笔盈亏': '{:,.2f}',
                            '累计盈亏': '{:,.2f}'
                        }).map(
                            lambda x: 'color: green' if isinstance(x, (int, float)) and x > 0 
                            else 'color: red' if isinstance(x, (int, float)) and x < 0 
                            else '', subset=['单笔盈亏', '累计盈亏']
                        ),
                        height=300,
                        width='stretch'
                    )
                
                with col2:
                    # 交易统计
                    st.markdown("**📊 交易统计**")
                    
                    total_trades = len(trades_df)
                    buy_trades = len(trades_df[trades_df['action'] == 'BUY'])
                    sell_trades = len(trades_df[trades_df['action'] == 'SELL'])
                    
                    st.metric("总交易次数", total_trades)
                    st.metric("买入次数", buy_trades)
                    st.metric("卖出次数", sell_trades)
                    
                    if sell_trades > 0:
                        # 计算胜率
                        profitable_trades = len(trades_df[(trades_df['action'] == 'SELL') & (trades_df['单笔盈亏'] > 0)])
                        win_rate = (profitable_trades / sell_trades) * 100
                        st.metric("胜率", f"{win_rate:.1f}%")
                        
                        # 平均盈亏
                        avg_profit = trades_df[trades_df['action'] == 'SELL']['单笔盈亏'].mean()
                        st.metric("平均单笔盈亏", f"{avg_profit:,.2f}")
                        
                        # 最大单笔盈亏
                        max_profit = trades_df[trades_df['action'] == 'SELL']['单笔盈亏'].max()
                        max_loss = trades_df[trades_df['action'] == 'SELL']['单笔盈亏'].min()
                        
                        st.metric("最大单笔盈利", f"{max_profit:,.2f}")
                        st.metric("最大单笔亏损", f"{max_loss:,.2f}")
                    
                    # 今日交易统计
                    today = datetime.now().strftime('%Y-%m-%d')
                    today_trades = trades_df[trades_df['time'].str.contains(today)]
                    
                    if not today_trades.empty:
                        st.markdown("**📅 今日统计**")
                        today_total = len(today_trades)
                        today_buy = len(today_trades[today_trades['action'] == 'BUY'])
                        today_sell = len(today_trades[today_trades['action'] == 'SELL'])
                        
                        st.write(f"今日交易: {today_total} 笔")
                        st.write(f"买入: {today_buy} 笔")
                        st.write(f"卖出: {today_sell} 笔")
                        
                        if today_sell > 0:
                            today_pnl = today_trades[today_trades['action'] == 'SELL']['单笔盈亏'].sum()
                            st.write(f"今日盈亏: {today_pnl:,.2f}")
            
            # 自动刷新
            time.sleep(2)
            st.rerun()
    
    else:
        st.info("👈 请在左侧配置参数并点击'启动'开始模拟交易")
        
        # 功能介绍
        st.header("🌟 功能特色")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            **🚀 实时模拟**
            - 基于真实tick数据
            - 增强版策略执行
            - 实时风险控制
            """)
        
        with col2:
            st.markdown("""
            **💼 自定义持仓**
            - 设置初始持仓数量
            - 指定持仓成本
            - 灵活的资金配置
            """)
        
        with col3:
            st.markdown("""
            **📊 直观监控**
            - 实时价格图表
            - 交易信号可视化
            - 净值变化跟踪
            """)

if __name__ == "__main__":
    main()