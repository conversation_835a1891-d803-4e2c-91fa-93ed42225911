#!/usr/bin/env python3
"""
调试胜率计算问题
"""

import pandas as pd
from backtest_enhanced import run_enhanced_backtest, BacktestConfig

def debug_win_rate():
    """调试胜率计算"""
    
    print("=== 调试胜率计算问题 ===")
    
    # 使用简单配置
    config = BacktestConfig(
        symbol='159740',
        start_date='2025-08-25',
        end_date='2025-09-01',
        initial_capital=1000000
    )
    
    print("运行回测...")
    results = run_enhanced_backtest(config)
    
    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return
    
    # 获取交易数据
    trades_df = results['raw_data']['trades']
    
    print(f"\n=== 交易数据分析 ===")
    print(f"总交易数: {len(trades_df)}")
    
    # 分析买入交易
    buy_trades = trades_df[trades_df['type'] == 'BUY']
    print(f"买入交易数: {len(buy_trades)}")
    
    # 分析卖出交易
    sell_trades = trades_df[trades_df['type'] == 'SELL']
    print(f"卖出交易数: {len(sell_trades)}")
    
    # 检查pnl列
    if 'pnl' in trades_df.columns:
        print(f"包含pnl的交易数: {len(trades_df[trades_df['pnl'].notna()])}")
        
        # 分析卖出交易的pnl
        sell_with_pnl = sell_trades[sell_trades['pnl'].notna()]
        print(f"有pnl的卖出交易数: {len(sell_with_pnl)}")
        
        if len(sell_with_pnl) > 0:
            profitable = sell_with_pnl[sell_with_pnl['pnl'] > 0]
            losing = sell_with_pnl[sell_with_pnl['pnl'] < 0]
            breakeven = sell_with_pnl[sell_with_pnl['pnl'] == 0]
            
            print(f"盈利交易数: {len(profitable)}")
            print(f"亏损交易数: {len(losing)}")
            print(f"盈亏平衡交易数: {len(breakeven)}")
            
            # 计算胜率
            if len(sell_with_pnl) > 0:
                win_rate = len(profitable) / len(sell_with_pnl)
                print(f"计算胜率: {win_rate:.2%}")
            
            # 显示前几笔卖出交易的详情
            print(f"\n=== 前10笔卖出交易详情 ===")
            for i, (idx, trade) in enumerate(sell_with_pnl.head(10).iterrows()):
                print(f"{i+1}. 时间: {trade['time']}, 数量: {trade['quantity']}, "
                      f"价格: {trade['price']:.4f}, PnL: {trade['pnl']:.2f}, "
                      f"原因: {trade['reason']}")
            
            # 检查是否所有卖出交易都是盈利的
            if len(losing) == 0:
                print(f"\n⚠️ 警告: 所有卖出交易都是盈利的，这可能不正常")
                print("可能的原因:")
                print("1. 策略参数设置过于保守，只在确定盈利时才卖出")
                print("2. 止损逻辑没有正确执行")
                print("3. 回测数据或逻辑有问题")
                
                # 检查是否有止损交易
                stop_loss_trades = sell_with_pnl[sell_with_pnl['reason'].str.contains('止损', na=False)]
                print(f"止损交易数: {len(stop_loss_trades)}")
                
                # 检查收盘平仓交易
                close_trades = sell_with_pnl[sell_with_pnl['reason'].str.contains('收盘', na=False)]
                print(f"收盘平仓交易数: {len(close_trades)}")
                if len(close_trades) > 0:
                    close_pnl = close_trades['pnl'].sum()
                    print(f"收盘平仓总盈亏: {close_pnl:.2f}")
    else:
        print("⚠️ 交易数据中没有pnl列")
    
    # 显示回测结果中的胜率
    perf = results['performance']
    print(f"\n=== 回测结果显示 ===")
    print(f"显示胜率: {perf['胜率']}")
    print(f"总收益率: {perf['总收益率']}")
    print(f"最大回撤: {perf['最大回撤']}")
    
    # 检查原始CSV文件
    csv_file = '2025-09-01T03-08_export.csv'
    try:
        csv_df = pd.read_csv(csv_file)
        print(f"\n=== CSV文件分析 ===")
        print(f"CSV总交易数: {len(csv_df)}")
        
        csv_sell = csv_df[csv_df['type'] == 'SELL']
        if len(csv_sell) > 0 and 'pnl' in csv_df.columns:
            csv_profitable = len(csv_sell[csv_sell['pnl'] > 0])
            csv_win_rate = csv_profitable / len(csv_sell)
            print(f"CSV胜率: {csv_win_rate:.2%}")
            print(f"CSV盈利交易: {csv_profitable}/{len(csv_sell)}")
        
    except FileNotFoundError:
        print("CSV文件不存在")

if __name__ == "__main__":
    debug_win_rate()
