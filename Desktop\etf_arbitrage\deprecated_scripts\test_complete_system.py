#!/usr/bin/env python3
"""
完整系统测试 - 验证参数统一化后的系统功能
"""

import logging
from strategy_config import StrategyConfig

def test_complete_system():
    """测试完整系统功能"""
    
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    print("=== 完整系统测试 ===")
    
    # 1. 测试统一配置系统
    print("\n1. 统一配置系统测试:")
    try:
        defaults = StrategyConfig.get_default_values()
        print(f"   ✅ 获取默认配置成功，共 {len(defaults)} 个参数")
        
        presets = list(StrategyConfig.STRATEGY_PRESETS.keys())
        print(f"   ✅ 预设配置: {presets}")
        
        # 测试参数验证
        test_config = {'stop_loss': -0.15}  # 超出范围
        errors = StrategyConfig.validate_config(test_config)
        if errors:
            print(f"   ✅ 参数验证正常工作")
        
    except Exception as e:
        print(f"   ❌ 统一配置系统错误: {e}")
        return False
    
    # 2. 测试回测配置
    print("\n2. 回测配置测试:")
    try:
        from backtest_enhanced import BacktestConfig
        
        # 测试默认配置
        config1 = BacktestConfig()
        print(f"   ✅ 默认配置创建成功")
        print(f"      止损线: {config1.stop_loss} (应为负值)")
        print(f"      止盈目标: {config1.profit_target} (应为正值)")
        
        # 测试自定义配置
        config2 = BacktestConfig(stop_loss=-0.03, profit_target=0.008)
        print(f"   ✅ 自定义配置创建成功")
        print(f"      自定义止损线: {config2.stop_loss}")
        print(f"      自定义止盈目标: {config2.profit_target}")
        
    except Exception as e:
        print(f"   ❌ 回测配置错误: {e}")
        return False
    
    # 3. 测试回测引擎
    print("\n3. 回测引擎测试:")
    try:
        from backtest_enhanced import EnhancedBacktest
        
        config = BacktestConfig(
            symbol="159740",
            start_date="2025-08-25",
            end_date="2025-08-27"
        )
        
        backtest = EnhancedBacktest(config)
        print(f"   ✅ 回测引擎创建成功")
        
        # 测试数据加载
        df = backtest.load_data()
        if not df.empty:
            print(f"   ✅ 数据加载成功，共 {len(df)} 条记录")
        else:
            print(f"   ⚠️  数据为空，但加载功能正常")
        
    except Exception as e:
        print(f"   ❌ 回测引擎错误: {e}")
        return False
    
    # 4. 测试策略引擎导入
    print("\n4. 策略引擎测试:")
    try:
        from strategy_engine_enhanced import (
            BUY_TRIGGER_DROP, PROFIT_TARGET, STOP_LOSS,
            Position, RiskManager
        )
        
        print(f"   ✅ 策略引擎导入成功")
        print(f"      BUY_TRIGGER_DROP: {BUY_TRIGGER_DROP}")
        print(f"      PROFIT_TARGET: {PROFIT_TARGET}")
        print(f"      STOP_LOSS: {STOP_LOSS}")
        
        # 测试Position类
        position = Position()
        print(f"   ✅ Position类创建成功")
        
        # 测试RiskManager类
        risk_manager = RiskManager(1000000)
        print(f"   ✅ RiskManager类创建成功")
        
    except Exception as e:
        print(f"   ❌ 策略引擎错误: {e}")
        return False
    
    # 5. 测试正负符号一致性
    print("\n5. 正负符号一致性测试:")
    
    # 从不同来源获取参数，验证一致性
    config_defaults = StrategyConfig.get_default_values()
    
    try:
        from strategy_engine_enhanced import BUY_TRIGGER_DROP as ENGINE_BUY_TRIGGER
        from strategy_engine_enhanced import STOP_LOSS as ENGINE_STOP_LOSS
        from strategy_engine_enhanced import PROFIT_TARGET as ENGINE_PROFIT_TARGET
        
        checks = [
            ('buy_trigger_drop', config_defaults['buy_trigger_drop'], ENGINE_BUY_TRIGGER),
            ('stop_loss', config_defaults['stop_loss'], ENGINE_STOP_LOSS),
            ('profit_target', config_defaults['profit_target'], ENGINE_PROFIT_TARGET)
        ]
        
        all_consistent = True
        for param_name, config_value, engine_value in checks:
            is_consistent = abs(config_value - engine_value) < 1e-10
            status = "✅" if is_consistent else "❌"
            print(f"   {param_name}: 配置={config_value}, 引擎={engine_value} {status}")
            if not is_consistent:
                all_consistent = False
        
        if all_consistent:
            print(f"   ✅ 所有参数在不同模块间保持一致")
        else:
            print(f"   ❌ 参数不一致，需要修复")
            return False
            
    except Exception as e:
        print(f"   ❌ 参数一致性检查错误: {e}")
        return False
    
    # 6. 测试预设配置功能
    print("\n6. 预设配置功能测试:")
    try:
        for preset_name in StrategyConfig.STRATEGY_PRESETS.keys():
            preset_config = StrategyConfig.get_preset_config(preset_name)
            
            # 验证关键参数的符号
            buy_trigger = preset_config['buy_trigger_drop']
            stop_loss = preset_config['stop_loss']
            profit_target = preset_config['profit_target']
            
            signs_correct = (buy_trigger < 0 and stop_loss < 0 and profit_target > 0)
            status = "✅" if signs_correct else "❌"
            
            print(f"   {preset_name}: 买入={buy_trigger:.1%}, 止损={stop_loss:.1%}, 止盈={profit_target:.1%} {status}")
            
            if not signs_correct:
                return False
                
    except Exception as e:
        print(f"   ❌ 预设配置测试错误: {e}")
        return False
    
    print(f"\n🎉 完整系统测试通过！")
    print(f"\n✅ 参数统一化成功完成:")
    print(f"   - 所有参数都统一在 strategy_config.py 中管理")
    print(f"   - 正负符号规范一致")
    print(f"   - 各模块间参数保持同步")
    print(f"   - 预设配置功能正常")
    print(f"   - 向后兼容性良好")
    
    return True

if __name__ == "__main__":
    success = test_complete_system()
    if not success:
        print(f"\n❌ 系统测试失败，需要进一步修复")
        exit(1)
    else:
        print(f"\n🚀 系统已准备就绪，可以正常使用！")