# strategy_realtime.py
"""
基于本地 SQLite ticks 数据的实时策略骨架（模拟分层买入与快速卖出）。
读取最新 ticks（tail），计算短期回报率作为触发条件，执行模拟买卖并打印交易日志。

用法: python strategy_realtime.py --symbol 159740
"""
import sqlite3
import time
from datetime import datetime, timedelta
import argparse
from pathlib import Path
from typing import List, Tuple

DB = Path(__file__).resolve().parent / "ticks.db"
SYMBOL = '159740'

# 策略参数
BUY_TRIGGER_DROP = -0.006
PROFIT_TARGET = 0.006  # 改为基于持仓收益目标
LAYERS = [0.4, 0.35, 0.25]
MAX_POSITION = 10000
SLIPPAGE_PCT = 0.0008
FEE_PCT = 0.00005

position = 0
avg_price = 0.0


def get_recent_ticks(symbol: str, span_seconds: int = 20) -> List[Tuple[str, float, int]]:
    conn = sqlite3.connect(DB)
    c = conn.cursor()
    since = (datetime.now() - timedelta(seconds=span_seconds)).isoformat()
    rows = c.execute("SELECT tick_time,price,volume FROM ticks WHERE symbol=? AND tick_time>=? ORDER BY tick_time ASC", (symbol, since)).fetchall()
    conn.close()
    return rows


def compute_short_return(ticks: List[Tuple[str, float, int]]) -> float:
    if not ticks or len(ticks) < 2:
        return 0.0
    try:
        p0 = float(ticks[0][1])
        p1 = float(ticks[-1][1])
        if p0 == 0.0:
            return 0.0
        r = (p1 - p0) / p0
        if r != r or r == float('inf') or r == float('-inf'):
            return 0.0
        return float(r)
    except Exception:
        return 0.0


def simulate_buy(qty: int, price: float) -> Tuple[float, float]:
    global position, avg_price
    actual_price = price * (1 + SLIPPAGE_PCT)
    cost = actual_price * qty * (1 + FEE_PCT)
    new_pos = position + qty
    if new_pos == 0:
        avg_price = 0
    else:
        avg_price = (avg_price*position + actual_price*qty)/new_pos
    position = new_pos
    return actual_price, cost


def simulate_sell(qty: int, price: float) -> Tuple[float, float]:
    global position, avg_price
    actual_price = price * (1 - SLIPPAGE_PCT)
    proceed = actual_price * qty * (1 - FEE_PCT)
    position -= qty
    if position <= 0:
        position = 0
        avg_price = 0
    return actual_price, proceed


def run(symbol: str) -> None:
    global position, avg_price
    while True:
        ticks = get_recent_ticks(symbol, span_seconds=20)
        r = compute_short_return(ticks)
        if r <= BUY_TRIGGER_DROP and position < MAX_POSITION:
            for layer_pct in LAYERS:
                qty = int(MAX_POSITION * layer_pct)
                price = ticks[-1][1] if ticks else 0
                if qty <= 0:
                    continue
                p, cost = simulate_buy(qty, price)
                print(f"{datetime.now()} BUY qty={qty} at {p:.4f} cost={cost:.2f} pos={position}")
            time.sleep(1)
        elif r >= SELL_TRIGGER_RISE and position > 0:
            qty = position
            price = ticks[-1][1] if ticks else avg_price
            p, proceed = simulate_sell(qty, price)
            print(f"{datetime.now()} SELL qty={qty} at {p:.4f} proceed={proceed:.2f} pos={position}")
        time.sleep(0.5)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--symbol', default='159740')
    args = parser.parse_args()
    run(args.symbol)
