# 📈 增强版回测面板 - 快速使用指南

## 🚀 快速开始

### 1. 启动面板
```bash
# 方法1: 使用启动脚本（推荐）
python run_enhanced_dashboard.py

# 方法2: 直接启动streamlit
streamlit run app_enhanced_backtest_dashboard.py
```

### 2. 访问界面
- 浏览器自动打开：`http://localhost:8501`
- 如果没有自动打开，请手动访问上述地址

## 📊 界面操作指南

### 左侧配置面板

#### 基本设置
- **交易标的**: 选择要回测的ETF（如159740）
- **时间范围**: 选择回测的开始和结束日期
- **初始资金**: 设置回测的初始资金（默认100万）

#### 策略参数
- **预设配置**: 
  - `保守`: 买入-0.4%, 止损-1.5%, 持仓30分钟
  - `默认`: 买入-0.6%, 止损-2.0%, 持仓60分钟  
  - `激进`: 买入-0.8%, 止损-2.5%, 持仓120分钟
  - `自定义`: 手动调整所有参数

- **自定义参数**（选择自定义时可调整）:
  - 买入触发跌幅: 价格下跌多少时买入
  - 止损线: 最大亏损容忍度
  - 最大持仓时间: 超时强制卖出
  - 手续费率: 交易成本
  - 滑点: 价格冲击成本

### 主界面展示

#### 📊 回测结果概览
- **总收益率**: 策略整体表现
- **最大回撤**: 最大亏损幅度
- **胜率**: 盈利交易占比
- **交易次数**: 总交易频率

#### 📈 详细指标
- **财务指标**: 资金变化、夏普比率等
- **交易统计**: 买卖次数、胜率等

#### 📊 可视化图表

**1. 策略执行详情**
- 上图: 价格走势 + 买卖点标记
- 中图: 持仓数量变化
- 下图: 交易信号强度（红色=买入信号，绿色=卖出信号）

**2. 净值表现**
- 上图: 净值曲线走势
- 下图: 回撤曲线（红色区域）

**3. 交易分析**
- 交易时间分布: 哪些时段交易最频繁
- 买卖比例: 买入vs卖出次数
- 交易原因: 各种交易触发原因统计
- 盈亏分布: 单笔交易盈亏情况

**4. 交易明细表**
- 完整的交易记录
- 包含时间、价格、数量、原因、盈亏等

## 🎯 使用技巧

### 参数调优建议
1. **新手用户**: 建议使用"保守"预设，风险较低
2. **经验用户**: 可以使用"默认"配置作为基准
3. **高级用户**: 使用"自定义"精细调整参数

### 分析重点
1. **收益率 vs 回撤**: 平衡收益和风险
2. **胜率 vs 交易频率**: 找到最佳交易频率
3. **时间分布**: 识别最佳交易时段
4. **信号质量**: 观察信号强度图，优化触发条件

### 图表交互
- **缩放**: 鼠标滚轮或拖拽选择时间范围
- **悬停**: 鼠标悬停查看详细数据
- **隐藏**: 点击图例隐藏/显示特定数据线
- **时间轴**: 底部滑块可快速跳转时间段

## ⚠️ 注意事项

### 数据要求
- 确保数据库中有对应标的的tick数据
- 建议至少有1-2天的完整数据进行回测
- 数据时间范围要覆盖选择的回测期间

### 性能优化
- 大数据量回测可能需要较长时间
- 建议先用较短时间段测试参数
- 避免同时运行多个回测任务

### 结果解读
- 回测结果仅供参考，不代表未来表现
- 考虑实际交易中的网络延迟、资金限制等因素
- 建议结合多个时间段的回测结果综合判断

## 🔧 故障排除

### 常见问题
1. **无法启动**: 检查是否安装streamlit (`pip install streamlit`)
2. **无数据显示**: 确认数据库中有对应标的的数据
3. **回测失败**: 检查时间范围是否有效，参数是否合理
4. **图表不显示**: 刷新页面或检查浏览器兼容性

### 技术支持
- 查看控制台错误信息
- 检查数据库连接状态
- 确认所有依赖包已正确安装

## 📚 进阶使用

### 批量回测
可以通过修改参数多次运行，比较不同配置的效果

### 参数优化
1. 固定其他参数，单独调整一个参数
2. 观察收益率和回撤的变化
3. 找到最佳参数组合

### 策略改进
根据回测结果识别策略弱点：
- 交易频率过高/过低
- 止损设置不合理
- 持仓时间需要调整
- 买入时机需要优化

---

🎉 **开始您的策略回测之旅吧！**