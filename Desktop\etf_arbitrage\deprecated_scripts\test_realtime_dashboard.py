#!/usr/bin/env python3
"""
测试实时监控页面
"""

import subprocess
import sys
import os

def test_realtime_dashboard():
    """测试实时监控页面"""
    print("=== 测试实时监控页面 ===")
    
    # 检查文件是否存在
    if not os.path.exists("app_enhanced_realtime_dashboard.py"):
        print("❌ 实时监控页面文件不存在")
        return False
    
    try:
        # 测试导入
        import app_enhanced_realtime_dashboard
        print("✅ 实时监控页面导入成功")
        
        # 测试关键函数
        from app_enhanced_realtime_dashboard import RealtimeSimulator, load_available_symbols
        
        # 测试模拟器初始化
        simulator = RealtimeSimulator()
        print("✅ 实时模拟器初始化成功")
        
        # 测试符号加载
        symbols = load_available_symbols()
        print(f"✅ 可用交易标的: {symbols}")
        
        print("\n=== 实时监控页面测试完成 ===")
        print("🚀 可以使用以下命令启动:")
        print("streamlit run app_enhanced_realtime_dashboard.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_realtime_dashboard()
    sys.exit(0 if success else 1)