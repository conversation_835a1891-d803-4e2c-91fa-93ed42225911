#!/usr/bin/env python3
"""
增强版回测仪表板
提供直观的策略执行效果展示，时间轴与tick数据完美契合
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime, timedelta
import sqlite3
from typing import Dict, List, Optional

# 导入增强版回测组件
from backtest_enhanced import BacktestConfig, EnhancedBacktest
# 导入统一配置
from strategy_config import StrategyConfig
# 导入新功能模块
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import asyncio
import sqlite3
import itertools
import numpy as np
from datetime import datetime, timedelta

# 页面配置
st.set_page_config(
    page_title="增强版策略回测仪表板",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.0rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: rgba(240, 242, 246, 0.7); /* 添加透明度 */
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
        transition: transform 0.2s ease, box-shadow 0.2s ease; /* 添加过渡动画 */
    }
    
    .metric-card:hover {
        transform: translateY(-2px); /* 悬停时上移 */
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15); /* 悬停时增强阴影 */
    }
    
    .metric-card.blue {
        background-color: rgba(31, 119, 180, 0.15); /* 蓝色半透明背景 */
        border-left: 4px solid #1f77b4;
    }
    
    .metric-card.green {
        background-color: rgba(44, 160, 44, 0.15); /* 绿色半透明背景 */
        border-left: 4px solid #2ca02c;
    }
    
    .metric-card.red {
        background-color: rgba(214, 40, 40, 0.15); /* 红色半透明背景 */
        border-left: 4px solid #d62728;
    }
    
    .metric-card.orange {
        background-color: rgba(255, 127, 14, 0.15); /* 橙色半透明背景 */
        border-left: 4px solid #ff7f0e;
    }
    
    .metric-card.purple {
        background-color: rgba(148, 103, 189, 0.15); /* 紫色半透明背景 */
        border-left: 4px solid #9467bd;
    }
    .positive {
        color: #00cc00;
    }
    .negative {
        color: #ff0000;
    }
    .neutral {
        color: #666666;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_available_symbols():
    """加载可用的交易标的"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute("SELECT DISTINCT symbol FROM ticks ORDER BY symbol")
        symbols = [row[0] for row in cursor.fetchall()]
        conn.close()
        return symbols
    except:
        return ["159740"]

@st.cache_data
def get_data_date_range(symbol: str):
    """获取数据的日期范围"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute(
            "SELECT MIN(DATE(tick_time)) as min_date, MAX(DATE(tick_time)) as max_date FROM ticks WHERE symbol=?",
            (symbol,)
        )
        result = cursor.fetchone()
        conn.close()
        if result and result[0] and result[1]:
            return result[0], result[1]
        return None, None
    except:
        return None, None

def run_enhanced_backtest(config: BacktestConfig) -> Dict:
    """运行增强版回测"""
    backtest = EnhancedBacktest(config)
    return backtest.run_backtest()

def create_price_and_signals_chart(df_signals: pd.DataFrame, df_trades: pd.DataFrame, config: BacktestConfig) -> go.Figure:
    """创建价格和信号图表"""
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('价格走势与交易信号', '持仓变化', '交易信号强度'),
        row_heights=[0.5, 0.25, 0.25]
    )
    
    # 价格走势
    fig.add_trace(
        go.Scatter(
            x=df_signals['time'],
            y=df_signals['price'],
            mode='lines',
            name='价格',
            line=dict(color='blue', width=1),
            hovertemplate='时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 交易点位
    if not df_trades.empty:
        buy_trades = df_trades[df_trades['type'] == 'BUY']
        sell_trades = df_trades[df_trades['type'] == 'SELL']
        
        if not buy_trades.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_trades['time'],
                    y=buy_trades['price'],
                    mode='markers',
                    name='买入',
                    marker=dict(
                        symbol='triangle-up',
                        size=12,
                        color='green',
                        line=dict(width=2, color='darkgreen')
                    ),
                    hovertemplate='买入<br>时间: %{x}<br>价格: %{y:.4f}<br>数量: %{customdata}<extra></extra>',
                    customdata=buy_trades['quantity']
                ),
                row=1, col=1
            )
        
        if not sell_trades.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_trades['time'],
                    y=sell_trades['price'],
                    mode='markers',
                    name='卖出',
                    marker=dict(
                        symbol='triangle-down',
                        size=12,
                        color='red',
                        line=dict(width=2, color='darkred')
                    ),
                    hovertemplate='卖出<br>时间: %{x}<br>价格: %{y:.4f}<br>数量: %{customdata}<extra></extra>',
                    customdata=sell_trades['quantity']
                ),
                row=1, col=1
            )
    
    # 持仓变化
    fig.add_trace(
        go.Scatter(
            x=df_signals['time'],
            y=df_signals['position'],
            mode='lines',
            name='持仓',
            line=dict(color='orange', width=2),
            fill='tonexty',
            hovertemplate='时间: %{x}<br>持仓: %{y}<extra></extra>'
        ),
        row=2, col=1
    )
    
    # 交易信号强度
    # 交易信号强度 - 使用配置参数
    buy_threshold = config.buy_trigger_drop
    profit_threshold = config.profit_target
    
    signal_colors = [
        'green' if s <= buy_threshold 
        else 'red' if s >= profit_threshold 
        else 'gray' 
        for s in df_signals['signal']
    ]
    
    fig.add_trace(
        go.Scatter(
            x=df_signals['time'],
            y=df_signals['signal'],
            mode='markers',
            name='信号',
            marker=dict(
                color=signal_colors,
                size=4,
                opacity=0.6
            ),
            hovertemplate='时间: %{x}<br>信号: %{y:.6f}<extra></extra>'
        ),
        row=3, col=1
    )
    
    # 添加参数化的信号阈值线
    fig.add_hline(y=buy_threshold, line_dash="dash", line_color="red", 
                  annotation_text=f"买入阈值: {buy_threshold:.4f}", row=3, col=1)
    fig.add_hline(y=profit_threshold, line_dash="dash", line_color="green", 
                  annotation_text=f"止盈阈值: {profit_threshold:.4f}", row=3, col=1)
    
    # 更新布局
    fig.update_layout(
        # title="增强版策略执行详情",
        height=800,
        showlegend=True,
        hovermode='x unified',
        xaxis3=dict(
            title="时间",
            rangeslider=dict(visible=True, thickness=0.05),
            type='date',
            rangebreaks=[
                dict(bounds=["sat", "mon"]),  # 隐藏周末
                dict(bounds=[16, 9], pattern="hour"),  # 隐藏非交易时间
            ]
        )
    )
    
    fig.update_yaxes(title_text="价格", row=1, col=1)
    fig.update_yaxes(title_text="持仓数量", row=2, col=1)
    fig.update_yaxes(title_text="信号强度", row=3, col=1)
    
    return fig

def create_equity_curve_chart(df_equity: pd.DataFrame, config: BacktestConfig) -> go.Figure:
    """创建净值曲线图表"""
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=('净值曲线', '回撤曲线'),
        row_heights=[0.7, 0.3]
    )
    
    # 净值曲线
    fig.add_trace(
        go.Scatter(
            x=df_equity['time'],
            y=df_equity['equity'],
            mode='lines',
            name='净值',
            line=dict(color='blue', width=2),
            hovertemplate='时间: %{x}<br>净值: %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 初始资金线
    fig.add_hline(
        y=config.initial_capital,
        line_dash="dash",
        line_color="gray",
        annotation_text=f"初始资金: {config.initial_capital:,.0f}",
        row=1, col=1
    )
    
    # 回撤曲线
    peak = df_equity['equity'].cummax()
    drawdown = (df_equity['equity'] - peak) / peak
    
    fig.add_trace(
        go.Scatter(
            x=df_equity['time'],
            y=drawdown,
            mode='lines',
            name='回撤',
            line=dict(color='red', width=1),
            fill='tonexty',
            fillcolor='rgba(255,0,0,0.3)',
            hovertemplate='时间: %{x}<br>回撤: %{y:.2%}<extra></extra>'
        ),
        row=2, col=1
    )
    
    # 更新布局
    fig.update_layout(
        # title="策略净值表现",
        height=600,
        showlegend=True,
        hovermode='x unified',
        xaxis2=dict(
            title="时间",
            type='date',
            rangebreaks=[
                dict(bounds=["sat", "mon"]),  # 隐藏周末
                dict(bounds=[16, 9], pattern="hour"),  # 隐藏非交易时间
            ]
        )
    )
    
    fig.update_yaxes(title_text="净值", row=1, col=1)
    fig.update_yaxes(title_text="回撤", tickformat='.2%', row=2, col=1)
    
    return fig

def create_trade_analysis_chart(df_trades: pd.DataFrame) -> go.Figure:
    """创建交易分析图表"""
    if df_trades.empty:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无交易数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=20)
        )
        return fig
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('交易时间分布', '买卖比例', '交易原因分析', '盈亏分布'),
        specs=[[{"type": "histogram"}, {"type": "pie"}],
               [{"type": "bar"}, {"type": "histogram"}]]
    )
    
    # 交易时间分布
    df_trades['hour'] = pd.to_datetime(df_trades['time']).dt.hour
    fig.add_trace(
        go.Histogram(
            x=df_trades['hour'],
            nbinsx=24,
            name='交易时间',
            marker_color='lightblue'
        ),
        row=1, col=1
    )
    
    # 买卖比例
    trade_counts = df_trades['type'].value_counts()
    fig.add_trace(
        go.Pie(
            labels=trade_counts.index,
            values=trade_counts.values,
            name='买卖比例',
            marker_colors=['green', 'red']
        ),
        row=1, col=2
    )
    
    # 交易原因分析
    if 'reason' in df_trades.columns:
        reason_counts = df_trades['reason'].value_counts()
        fig.add_trace(
            go.Bar(
                x=reason_counts.index,
                y=reason_counts.values,
                name='交易原因',
                marker_color='orange'
            ),
            row=2, col=1
        )
    
    # 盈亏分布
    sell_trades = df_trades[df_trades['type'] == 'SELL']
    if not sell_trades.empty and 'pnl' in sell_trades.columns:
        fig.add_trace(
            go.Histogram(
                x=sell_trades['pnl'],
                name='盈亏分布',
                marker_color='purple'
            ),
            row=2, col=2
        )
    
    fig.update_layout(
        title="交易行为分析",
        height=600,
        showlegend=False
    )
    
    return fig

def analyze_tick_volatility(symbol: str, start_date: str, end_date: str, window: int = 20) -> Dict:
    """分析tick数据波动统计（基于策略窗口）"""
    try:
        conn = sqlite3.connect("ticks.db")
        
        query = """
        SELECT tick_time as time, price, volume 
        FROM ticks 
        WHERE symbol=? AND tick_time BETWEEN ? AND ?
        ORDER BY tick_time ASC
        """
        
        df = pd.read_sql_query(
            query, conn, 
            params=[symbol, start_date, end_date],
            parse_dates=['time']
        )
        conn.close()
        
        if df.empty:
            return {'error': '无数据'}
        
        df['price'] = pd.to_numeric(df['price'], errors='coerce')
        df = df.dropna(subset=['price'])
        
        if len(df) < window:
            return {'error': f'数据不足，需要至少{window}个tick'}
        
        # 基于20个tick窗口的分析（与策略一致）
        window_returns = []
        window_drawdowns = []
        
        for i in range(window, len(df)):
            # 计算20个tick窗口的收益率（与策略信号计算一致）
            window_data = df.iloc[i-window:i+1]
            p0 = float(window_data['price'].iloc[0])
            p1 = float(window_data['price'].iloc[-1])
            
            if p0 > 0:
                window_return = (p1 - p0) / p0
                window_returns.append(window_return)
            
            # 计算20个tick窗口内的最大回撤
            window_high = float(window_data['price'].max())
            window_current = float(window_data['price'].iloc[-1])
            
            if window_high > 0:
                window_drawdown = (window_current - window_high) / window_high
                window_drawdowns.append(window_drawdown)
        
        window_returns = np.array(window_returns)
        window_drawdowns = np.array(window_drawdowns)
        
        # 统计分析
        analysis = {
            '数据概况': {
                'tick总数': len(df),
                f'{window}tick窗口数': len(window_returns),
                '价格范围': f"{df['price'].min():.4f} - {df['price'].max():.4f}",
                '平均价格': f"{df['price'].mean():.4f}"
            },
            f'{window}tick窗口波动分析': {
                f'平均{window}tick收益率': f"{window_returns.mean():.6f} ({window_returns.mean()*100:.4f}%)",
                f'{window}tick收益率标准差': f"{window_returns.std():.6f} ({window_returns.std()*100:.4f}%)",
                f'最大{window}tick涨幅': f"{window_returns.max():.4f} ({window_returns.max()*100:.2f}%)",
                f'最小{window}tick跌幅': f"{window_returns.min():.4f} ({window_returns.min()*100:.2f}%)"
            },
            f'{window}tick窗口回撤分析': {
                f'最大{window}tick回撤': f"{window_drawdowns.min():.4f} ({window_drawdowns.min()*100:.2f}%)",
                f'平均{window}tick回撤': f"{window_drawdowns.mean():.4f} ({window_drawdowns.mean()*100:.2f}%)",
                f'{window}tick回撤标准差': f"{window_drawdowns.std():.4f} ({window_drawdowns.std()*100:.2f}%)"
            },
            '策略参数建议': {
                '建议买入触发跌幅': f"{np.percentile(window_drawdowns, 10):.4f} ({np.percentile(window_drawdowns, 10)*100:.2f}%)",
                '建议止盈目标': f"{np.percentile(window_returns, 90):.4f} ({np.percentile(window_returns, 90)*100:.2f}%)",
                '建议止损线': f"{np.percentile(window_drawdowns, 5):.4f} ({np.percentile(window_drawdowns, 5)*100:.2f}%)"
            },
            'raw_data': {
                'original_df': df,
                'window_returns': window_returns,
                'window_drawdowns': window_drawdowns
            }
        }
        
        return analysis
        
    except Exception as e:
        return {'error': f'分析失败: {str(e)}'}


# 智能参数优化相关函数
def get_db_connection():
    """获取数据库连接"""
    db_path = "ticks.db"
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def load_saved_optimal_configs(symbol: str):
    """加载已保存的优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT config_name, parameters, performance_metrics, fitness_score,
                   optimization_method, created_at
            FROM optimal_configs
            WHERE symbol=?
            ORDER BY fitness_score DESC
            LIMIT 10
        """, (symbol,))

        configs = []
        for row in cursor.fetchall():
            configs.append({
                'config_name': row['config_name'],
                'parameters': json.loads(row['parameters']),
                'performance_metrics': json.loads(row['performance_metrics']),
                'fitness_score': row['fitness_score'] or 0.0,
                'optimization_method': row['optimization_method'],
                'created_at': row['created_at'],
                'symbol': symbol  # 添加symbol字段
            })

        conn.close()
        return configs
    except Exception as e:
        st.error(f"加载优化配置失败: {e}")
        return []

def load_optimal_config(config_data):
    """加载选中的优化配置到session state"""
    try:
        params = config_data['parameters']

        # 更新session state中的参数
        st.session_state.buy_trigger = params.get('buy_trigger_drop', -0.006)
        st.session_state.profit_target = params.get('profit_target', 0.0025)
        st.session_state.stop_loss = params.get('stop_loss', -0.015)
        st.session_state.max_hold_time = params.get('max_hold_time', 1800)

        # 显示加载成功信息
        metrics = config_data['performance_metrics']
        st.success(f"""
        ✅ 已加载优化配置: {config_data['config_name']}

        **参数设置:**
        - 买入触发: {params.get('buy_trigger_drop', -0.006):.3f}
        - 止盈目标: {params.get('profit_target', 0.0025):.3f}
        - 止损线: {params.get('stop_loss', -0.015):.3f}
        - 最大持仓时间: {params.get('max_hold_time', 1800)}秒

        **预期性能:**
        - 适应度分数: {config_data['fitness_score']:.3f}
        - 总收益率: {metrics.get('total_return', 0):.2%}
        - 最大回撤: {metrics.get('max_drawdown', 0):.2%}
        - 胜率: {metrics.get('win_rate', 0):.1%}
        """)

        # 强制刷新页面以应用新参数
        st.rerun()

    except Exception as e:
        st.error(f"加载配置失败: {e}")

def run_parameter_optimization(symbol: str, method: str, days: int, strategy_type: str):
    """运行真正的参数优化"""

    # 显示优化进度
    progress_container = st.container()
    with progress_container:
        st.info("🔍 正在进行智能参数优化，请稍候...")
        progress_bar = st.progress(0)
        status_text = st.empty()

    try:

        # 定义参数空间 - 真正的网格搜索
        param_space = {
            'conservative': {
                'buy_trigger_drop': (-0.008, -0.005, 0.001),  # 减少步长，提高精度
                'profit_target': (0.003, 0.006, 0.001),
                'stop_loss': (-0.025, -0.015, 0.002)
            },
            'balanced': {
                'buy_trigger_drop': (-0.010, -0.005, 0.001),
                'profit_target': (0.004, 0.008, 0.001),
                'stop_loss': (-0.030, -0.015, 0.003)
            },
            'aggressive': {
                'buy_trigger_drop': (-0.012, -0.007, 0.001),
                'profit_target': (0.005, 0.010, 0.001),
                'stop_loss': (-0.035, -0.020, 0.003)
            }
        }

        # 固定最大持仓时间
        FIXED_MAX_HOLD_TIME = 86400  # 24小时

        # 真正的网格搜索优化
        status_text.text("🔍 生成参数组合...")
        progress_bar.progress(0.1)

        # 生成参数网格
        space = param_space[strategy_type]
        param_combinations = generate_parameter_grid(space)
        total_combinations = len(param_combinations)

        status_text.text(f"📊 开始评估 {total_combinations} 个参数组合...")
        progress_bar.progress(0.2)

        # 评估每个参数组合
        best_params = None
        best_performance = None
        best_fitness = -999

        for i, params in enumerate(param_combinations):
            # 添加固定的最大持仓时间
            params['max_hold_time'] = FIXED_MAX_HOLD_TIME

            # 执行真正的回测
            try:
                performance = evaluate_parameter_combination(symbol, params, days)

                # 计算适应度分数
                fitness = calculate_fitness_score(performance)

                if fitness > best_fitness:
                    best_fitness = fitness
                    best_params = params.copy()
                    best_performance = performance.copy()

                # 更新进度
                progress = 0.2 + 0.7 * (i + 1) / total_combinations
                progress_bar.progress(progress)
                status_text.text(f"📈 评估进度: {i+1}/{total_combinations} (当前最佳适应度: {best_fitness:.3f})")

            except Exception as e:
                st.warning(f"参数组合 {i+1} 评估失败: {e}")
                continue

        if best_params is None:
            raise Exception("所有参数组合评估都失败了")

        optimal_params = best_params
        performance_metrics = best_performance
        fitness_score = best_fitness

        status_text.text("💾 保存优化结果...")
        progress_bar.progress(0.95)

        # 保存优化结果到数据库
        save_optimal_config(symbol, method, strategy_type, optimal_params, performance_metrics, fitness_score)

        # 清除进度显示
        progress_container.empty()

        # 显示优化结果
        st.success(f"""
        🎉 参数优化完成！

        **最优参数配置:**
        - 买入触发: {optimal_params['buy_trigger_drop']:.4f}
        - 止盈目标: {optimal_params['profit_target']:.4f}
        - 止损线: {optimal_params['stop_loss']:.4f}
        - 最大持仓时间: {optimal_params['max_hold_time']}秒 (固定为24小时)

        **预期性能指标:**
        - 总收益率: {performance_metrics['total_return']:.2%}
        - 最大回撤: {performance_metrics['max_drawdown']:.2%}
        - 夏普比率: {performance_metrics['sharpe_ratio']:.2f}
        - 胜率: {performance_metrics['win_rate']:.1%}
        - 适应度分数: {fitness_score:.3f}

        **优化说明:**
        - 核心参数(买入触发、止盈、止损)已优化
        - 最大持仓时间固定为24小时，给予充分的价格回归时间

        配置已保存，可在"已保存的优化结果"中选择使用。
        """)

    except Exception as e:
        progress_container.empty()
        st.error(f"参数优化失败: {e}")

def generate_parameter_grid(space: dict) -> list:
    """生成参数网格"""
    import numpy as np

    param_names = list(space.keys())
    param_ranges = []

    for param_name in param_names:
        min_val, max_val, step = space[param_name]
        values = np.arange(min_val, max_val + step, step)
        param_ranges.append(values)

    # 生成所有组合
    combinations = []
    for combo in itertools.product(*param_ranges):
        param_dict = dict(zip(param_names, combo))
        combinations.append(param_dict)

    # 限制组合数量，避免过多
    if len(combinations) > 50:
        # 随机采样50个组合
        import random
        combinations = random.sample(combinations, 50)

    return combinations

def evaluate_parameter_combination(symbol: str, params: dict, days: int) -> dict:
    """评估参数组合的性能"""
    try:
        # 计算日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)

        # 创建回测配置 - 包含日期参数
        config = BacktestConfig(
            symbol=symbol,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            buy_trigger_drop=params['buy_trigger_drop'],
            profit_target=params['profit_target'],
            stop_loss=params['stop_loss'],
            max_hold_time=params['max_hold_time'],
            initial_capital=100000,
            max_position_ratio=0.95
        )

        # 执行回测
        backtest = EnhancedBacktest(config)

        # 执行回测 - 不需要传递日期参数
        results = backtest.run_backtest()

        # 检查回测结果
        if results is None or 'error' in results:
            error_msg = results.get('error', '未知错误') if results else '回测返回None'
            st.warning(f"回测失败: {error_msg}")
            return {
                'total_return': -0.1,
                'max_drawdown': -0.1,
                'sharpe_ratio': 0,
                'win_rate': 0,
                'total_trades': 0
            }

        # 从回测结果中提取性能指标
        if 'performance' in results:
            # 直接使用回测引擎计算的性能指标，需要转换格式
            perf = results['performance']

            def parse_percentage(value):
                """解析百分比字符串为浮点数"""
                if isinstance(value, str):
                    return float(value.replace('%', '').replace(',', '')) / 100
                return float(value) if value else 0

            def parse_number(value):
                """解析数字字符串为浮点数"""
                if isinstance(value, str):
                    return float(value.replace(',', ''))
                return float(value) if value else 0

            return {
                'total_return': parse_percentage(perf.get('总收益率', '0%')),
                'max_drawdown': parse_percentage(perf.get('最大回撤', '0%')),
                'sharpe_ratio': parse_number(perf.get('夏普比率', '0')),
                'win_rate': parse_percentage(perf.get('胜率', '0%')),
                'total_trades': int(parse_number(perf.get('总交易次数', '0')))
            }
        else:
            # 如果没有性能指标，尝试从原始数据计算
            equity_df = results.get('data', {}).get('equity', pd.DataFrame())
            if not equity_df.empty:
                performance = calculate_performance_metrics(equity_df, config.initial_capital)
                return performance
            else:
                return {
                    'total_return': -0.1,
                    'max_drawdown': -0.1,
                    'sharpe_ratio': 0,
                    'win_rate': 0,
                    'total_trades': 0
                }

    except Exception as e:
        st.warning(f"回测执行失败: {e}")
        return {
            'total_return': -0.1,
            'max_drawdown': -0.1,
            'sharpe_ratio': 0,
            'win_rate': 0,
            'total_trades': 0
        }

def calculate_performance_metrics(equity_df: pd.DataFrame, initial_capital: float) -> dict:
    """计算性能指标"""
    try:
        if equity_df.empty:
            return {
                'total_return': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0,
                'win_rate': 0,
                'total_trades': 0
            }

        # 确保有equity列
        if 'equity' not in equity_df.columns:
            return {
                'total_return': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0,
                'win_rate': 0,
                'total_trades': 0
            }

        # 计算总收益率
        final_equity = equity_df['equity'].iloc[-1]
        total_return = (final_equity - initial_capital) / initial_capital

        # 计算最大回撤
        peak = equity_df['equity'].expanding().max()
        drawdown = (equity_df['equity'] - peak) / peak
        max_drawdown = drawdown.min()

        # 计算夏普比率
        returns = equity_df['equity'].pct_change().dropna()
        if len(returns) > 1 and returns.std() > 0:
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252)
        else:
            sharpe_ratio = 0

        # 胜率和交易次数需要从其他地方获取，这里设置默认值
        win_rate = 0.5  # 默认50%胜率
        total_trades = max(1, len(equity_df) // 100)  # 估算交易次数

        return {
            'total_return': float(total_return),
            'max_drawdown': float(max_drawdown),
            'sharpe_ratio': float(sharpe_ratio),
            'win_rate': float(win_rate),
            'total_trades': int(total_trades)
        }

    except Exception:
        return {
            'total_return': 0,
            'max_drawdown': 0,
            'sharpe_ratio': 0,
            'win_rate': 0,
            'total_trades': 0
        }

def calculate_fitness_score(performance: dict) -> float:
    """计算适应度分数"""
    try:
        # 综合评分公式
        fitness = (
            performance['total_return'] * 0.4 +  # 总收益率权重40%
            (1 + performance['max_drawdown']) * 0.3 +  # 最大回撤权重30%
            min(performance['sharpe_ratio'] / 2, 1) * 0.2 +  # 夏普比率权重20%
            performance['win_rate'] * 0.1  # 胜率权重10%
        )

        # 惩罚交易次数过少的情况
        if performance['total_trades'] < 5:
            fitness *= 0.5

        return float(fitness)

    except Exception:
        return -999

def save_optimal_config(symbol: str, method: str, strategy_type: str,
                       params: dict, metrics: dict, fitness: float):
    """保存优化配置到数据库"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        config_name = f"{strategy_type}_{method}_{datetime.now().strftime('%m%d_%H%M')}"

        cursor.execute("""
            INSERT OR REPLACE INTO optimal_configs
            (symbol, config_name, parameters, performance_metrics,
             optimization_method, fitness_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol,
            config_name,
            json.dumps(params),
            json.dumps(metrics),
            method,
            fitness,
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))

        conn.commit()
        conn.close()

    except Exception as e:
        st.error(f"保存优化配置失败: {e}")

def delete_optimal_config(config_name: str, symbol: str) -> bool:
    """删除优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            DELETE FROM optimal_configs
            WHERE config_name = ? AND symbol = ?
        """, (config_name, symbol))

        deleted_rows = cursor.rowcount
        conn.commit()
        conn.close()

        return deleted_rows > 0

    except Exception as e:
        st.error(f"删除优化配置失败: {e}")
        return False

def update_optimal_config(config_name: str, symbol: str, params: dict,
                         metrics: dict, fitness: float) -> bool:
    """更新优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE optimal_configs
            SET parameters = ?, performance_metrics = ?, fitness_score = ?, updated_at = ?
            WHERE config_name = ? AND symbol = ?
        """, (
            json.dumps(params),
            json.dumps(metrics),
            fitness,
            datetime.now().isoformat(),
            config_name,
            symbol
        ))

        updated_rows = cursor.rowcount
        conn.commit()
        conn.close()

        return updated_rows > 0

    except Exception as e:
        st.error(f"更新优化配置失败: {e}")
        return False

def edit_config_interface(config: dict, symbol: str):
    """编辑配置界面"""
    st.sidebar.markdown("---")
    st.sidebar.markdown("**✏️ 编辑配置:**")

    # 解析当前参数 - 处理不同的数据类型
    if isinstance(config['parameters'], str):
        # 如果是字符串，需要JSON解析
        current_params = json.loads(config['parameters'])
    elif isinstance(config['parameters'], dict):
        # 如果已经是字典，直接使用
        current_params = config['parameters']
    else:
        # 其他情况，尝试转换
        try:
            current_params = json.loads(str(config['parameters']))
        except:
            # 如果都失败，使用默认值
            current_params = {
                'buy_trigger_drop': -0.006,
                'profit_target': 0.005,
                'stop_loss': -0.02,
                'max_hold_time': 86400
            }

    # 编辑参数
    st.sidebar.markdown("**参数调整:**")

    new_buy_trigger = st.sidebar.number_input(
        "买入触发跌幅",
        min_value=-0.05,
        max_value=-0.001,
        value=float(current_params.get('buy_trigger_drop', -0.006)),
        step=0.001,
        format="%.3f",
        key="edit_buy_trigger"
    )

    new_profit_target = st.sidebar.number_input(
        "止盈目标",
        min_value=0.001,
        max_value=0.05,
        value=float(current_params.get('profit_target', 0.005)),
        step=0.001,
        format="%.3f",
        key="edit_profit_target"
    )

    new_stop_loss = st.sidebar.number_input(
        "止损线",
        min_value=-0.1,
        max_value=-0.005,
        value=float(current_params.get('stop_loss', -0.02)),
        step=0.001,
        format="%.3f",
        key="edit_stop_loss"
    )

    # 操作按钮
    col1, col2 = st.sidebar.columns(2)

    with col1:
        if st.button("💾 保存", key="save_edit"):
            # 更新参数
            updated_params = current_params.copy()
            updated_params['buy_trigger_drop'] = new_buy_trigger
            updated_params['profit_target'] = new_profit_target
            updated_params['stop_loss'] = new_stop_loss

            # 重新计算适应度（简化版）
            new_fitness = (
                new_profit_target * 0.4 +
                (1 + new_stop_loss) * 0.3 +
                0.5 * 0.3  # 假设其他指标
            )

            # 更新数据库 - 处理性能指标数据类型
            if isinstance(config['performance_metrics'], str):
                current_metrics = json.loads(config['performance_metrics'])
            elif isinstance(config['performance_metrics'], dict):
                current_metrics = config['performance_metrics']
            else:
                current_metrics = {}
            if update_optimal_config(
                config['config_name'],
                symbol,
                updated_params,
                current_metrics,
                new_fitness
            ):
                st.success("配置已更新")
                st.session_state.editing_config = False
                st.rerun()
            else:
                st.error("更新失败")

    with col2:
        if st.button("❌ 取消", key="cancel_edit"):
            st.session_state.editing_config = False
            st.rerun()

def main():
    """主函数"""
    st.markdown('<h1 class="main-header">📈 增强版策略回测仪表板</h1>', unsafe_allow_html=True)
    
    # 侧边栏配置
    st.sidebar.header("📊 回测配置")
    
    # 基本参数
    symbols = load_available_symbols()
    symbol = st.sidebar.selectbox("交易标的", symbols, index=0)
    
    # 获取数据日期范围
    min_date, max_date = get_data_date_range(symbol)
    if min_date and max_date:
        min_date = datetime.strptime(min_date, '%Y-%m-%d').date()
        max_date = datetime.strptime(max_date, '%Y-%m-%d').date()
        
        start_date = st.sidebar.date_input(
            "开始日期", 
            value=min_date,
            min_value=min_date,
            max_value=max_date
        )
        end_date = st.sidebar.date_input(
            "结束日期", 
            value=max_date,
            min_value=min_date,
            max_value=max_date
        )
    else:
        st.sidebar.error("无法获取数据日期范围")
        return
    
    # 使用统一配置的初始资金参数
    initial_capital_config = StrategyConfig.get_streamlit_config('initial_capital')
    initial_capital = st.sidebar.number_input(
        "初始资金", 
        **initial_capital_config
    )
    
    # 策略参数
    st.sidebar.subheader("🎯 策略参数")
    
    # 预设配置选择
    preset_options = ["自定义", "智能优化"] + list(StrategyConfig.STRATEGY_PRESETS.keys())
    config_preset = st.sidebar.selectbox(
        "预设配置",
        preset_options
    )

    # 智能参数优化功能
    if config_preset == "智能优化":
        st.sidebar.markdown("---")
        st.sidebar.subheader("🤖 智能参数优化")

        # 优化方法选择
        optimization_method = st.sidebar.selectbox(
            "优化方法",
            ["网格搜索", "遗传算法"],
            help="选择参数优化算法"
        )

        # 优化天数
        optimization_days = st.sidebar.slider(
            "优化数据天数",
            min_value=7,
            max_value=90,
            value=30,
            step=7,
            help="用于参数优化的历史数据天数"
        )

        # 策略类型
        strategy_type = st.sidebar.selectbox(
            "策略类型",
            ["conservative", "balanced", "aggressive"],
            index=1,
            help="保守型、平衡型或激进型策略"
        )

        # 优化说明
        st.sidebar.info("""
        **优化范围说明:**
        - ✅ 买入触发跌幅
        - ✅ 止盈目标
        - ✅ 止损线
        - 🔒 最大持仓时间 (固定4小时)

        *最大持仓时间固定为24小时，给予ETF套利策略充分的价格回归时间*
        """)

        # 运行优化按钮
        if st.sidebar.button("🔍 开始智能优化", type="secondary"):
            run_parameter_optimization(symbol, optimization_method, optimization_days, strategy_type)

        # 优化结果管理
        st.sidebar.markdown("---")
        st.sidebar.markdown("**📊 优化结果管理:**")
        saved_configs = load_saved_optimal_configs(symbol)

        if saved_configs:
            config_names = [f"{config['config_name']} (适应度: {config['fitness_score']:.3f})"
                          for config in saved_configs]
            selected_config_idx = st.sidebar.selectbox(
                "选择优化结果",
                range(len(config_names)),
                format_func=lambda x: config_names[x],
                help="选择之前优化的参数配置"
            )

            # 操作按钮行
            col1, col2, col3 = st.sidebar.columns(3)

            with col1:
                if st.button("📥 加载", key="load_config"):
                    load_optimal_config(saved_configs[selected_config_idx])
                    st.success("配置已加载")
                    st.rerun()

            with col2:
                if st.button("✏️ 编辑", key="edit_config"):
                    st.session_state.editing_config = True
                    st.session_state.edit_config_idx = selected_config_idx
                    st.rerun()

            with col3:
                if st.button("🗑️ 删除", key="delete_config"):
                    if delete_optimal_config(saved_configs[selected_config_idx]['config_name'], symbol):
                        st.success("配置已删除")
                        st.rerun()
                    else:
                        st.error("删除失败")

            # 编辑配置界面
            if getattr(st.session_state, 'editing_config', False):
                edit_config_interface(saved_configs[selected_config_idx], symbol)
        else:
            st.sidebar.info("暂无已保存的优化结果")

        # 使用session state中的参数（智能优化模式）
        buy_trigger = getattr(st.session_state, 'buy_trigger', -0.006)
        profit_target = getattr(st.session_state, 'profit_target', 0.0025)
        stop_loss = getattr(st.session_state, 'stop_loss', -0.015)
        max_hold_time = getattr(st.session_state, 'max_hold_time', 1800)

        # 显示当前使用的参数（智能优化模式）
        st.sidebar.markdown("---")
        st.sidebar.markdown("**📊 当前优化参数:**")
        st.sidebar.write(f"🎯 买入触发: {buy_trigger:.4f}")
        st.sidebar.write(f"💰 止盈目标: {profit_target:.4f}")
        st.sidebar.write(f"🛡️ 止损线: {stop_loss:.4f}")
        st.sidebar.write(f"⏱️ 最大持仓时间: {max_hold_time}秒 (固定)")
        st.sidebar.markdown("*参数来源: 智能优化 (持仓时间固定为24小时)*")

    elif config_preset in StrategyConfig.STRATEGY_PRESETS:
        # 预设配置模式
        preset_config = StrategyConfig.get_preset_config(config_preset)
        buy_trigger = preset_config['buy_trigger_drop']
        profit_target = preset_config['profit_target']
        stop_loss = preset_config['stop_loss']
        max_hold_time = preset_config['max_hold_time']
    else:  # 自定义模式
        defaults = StrategyConfig.get_default_values()
        buy_trigger = defaults['buy_trigger_drop']
        profit_target = defaults['profit_target']
        stop_loss = defaults['stop_loss']
        max_hold_time = defaults['max_hold_time']
    
    if config_preset == "自定义":
        # 使用统一配置的参数边界
        # 使用统一配置的参数边界
        trigger_config = StrategyConfig.get_streamlit_config('buy_trigger_drop')
        buy_trigger = st.sidebar.slider(
            "买入触发跌幅", 
            min_value=trigger_config['min_value'] * 100,
            max_value=trigger_config['max_value'] * 100,
            value=buy_trigger * 100, 
            step=trigger_config['step'] * 100,
            format="%.2f%%",
            help=trigger_config['help']
        ) / 100
        
        profit_config = StrategyConfig.get_streamlit_config('profit_target')
        profit_target = st.sidebar.slider(
            "止盈目标", 
            min_value=profit_config['min_value'] * 100,
            max_value=profit_config['max_value'] * 100,
            value=profit_target * 100, 
            step=profit_config['step'] * 100,
            format="%.2f%%",
            help=profit_config['help']
        ) / 100
        
        loss_config = StrategyConfig.get_streamlit_config('stop_loss')
        stop_loss = st.sidebar.slider(
            "止损线", 
            min_value=loss_config['min_value'] * 100,
            max_value=loss_config['max_value'] * 100,
            value=stop_loss * 100, 
            step=loss_config['step'] * 100,
            format="%.2f%%",
            help=loss_config['help']
        ) / 100
        
        holding_config = StrategyConfig.get_streamlit_config('max_hold_time')
        max_hold_time = st.sidebar.slider(
            "最大持仓时间", 
            min_value=int(holding_config['min_value'] ),
            max_value=int(holding_config['max_value'] ),
            value=int(max_hold_time), 
            step=int(holding_config['step'] ),
            format="%d秒",
            help=holding_config['help']
        )
    elif config_preset != "智能优化":  # 只在非智能优化模式下显示预设参数
        st.sidebar.markdown("---")
        st.sidebar.markdown("**📋 预设参数:**")
        st.sidebar.write(f"🎯 买入触发跌幅: {buy_trigger:.2%}")
        st.sidebar.write(f"💰 止盈目标: {profit_target:.2%}")
        st.sidebar.write(f"🛡️ 止损线: {abs(stop_loss):.2%}")  # 显示为正值
        st.sidebar.write(f"⏱️ 最大持仓时间: {max_hold_time}秒")
        st.sidebar.markdown(f"*参数来源: {config_preset}预设*")
    
    # 使用统一配置的手续费和滑点参数
    commission_config = StrategyConfig.get_streamlit_config('commission_rate')
    commission_rate = st.sidebar.slider(
        "手续费率", 
        min_value=commission_config['min_value'],
        max_value=commission_config['max_value'],
        value=commission_config['value'],
        step=commission_config['step'],
        format="%.4f",
        help=commission_config['help']
    )
    
    slippage_config = StrategyConfig.get_streamlit_config('slippage')
    slippage = st.sidebar.slider(
        "滑点", 
        min_value=slippage_config['min_value'],
        max_value=slippage_config['max_value'],
        value=slippage_config['value'],
        step=slippage_config['step'],
        format="%.4f",
        help=slippage_config['help']
    )
    
    # 高级参数设置
    # 高级参数设置
    st.sidebar.markdown("---")
    st.sidebar.header("🔧 高级参数")

    # 根据模式设置高级参数
    if config_preset == "智能优化":
        # 智能优化模式：使用固定的合理默认值，不显示控件
        max_position_config = StrategyConfig.get_param_config('max_position')
        max_position = int(max_position_config.default_value)
        st.sidebar.info("智能优化模式下，高级参数使用优化后的默认值")
    else:
        # 其他模式：显示参数控件
        max_position_config = StrategyConfig.get_param_config('max_position')
        max_position = st.sidebar.number_input(
            "最大持仓数量",
            min_value=int(max_position_config.min_value),
            max_value=int(max_position_config.max_value),
            value=int(max_position_config.default_value),
            step=int(max_position_config.step),
            format="%d"
        )
    
    if config_preset == "智能优化":
        # 智能优化模式：使用固定值
        position_size_config = StrategyConfig.get_param_config('position_size')
        position_size = int(position_size_config.default_value)

        signal_window_config = StrategyConfig.get_streamlit_config('signal_window')
        signal_window = int(signal_window_config['value'])

        min_hold_time = 30  # 固定值
    else:
        # 其他模式：显示参数控件
        position_size_config = StrategyConfig.get_param_config('position_size')
        position_size = st.sidebar.number_input(
            "单次买入仓位大小",
            min_value=int(position_size_config.min_value),
            max_value=int(position_size_config.max_value),
            value=int(position_size_config.default_value),
            step=int(position_size_config.step),
            format="%d"
        )

        # 技术指标参数
        signal_window_config = StrategyConfig.get_streamlit_config('signal_window')
        signal_window = st.sidebar.slider(
            "信号计算窗口(tick数)",
            min_value=int(signal_window_config['min_value']),
            max_value=int(signal_window_config['max_value']),
            value=int(signal_window_config['value']),
            step=int(signal_window_config['step']),
            format="%d个tick",
            help=signal_window_config['help']
        )

        # 最小持仓时间（使用自定义范围）
        min_hold_time = st.sidebar.slider(
            "最小持仓时间",
            min_value=10,
            max_value=300,
            value=30,
            step=10,
            format="%d秒",
            help="防止频繁交易的最小持仓时间保护"
        )
    
    # 分层买入参数
    st.sidebar.subheader("📊 分层买入设置")
    layer1_config = StrategyConfig.get_streamlit_config('layer1_ratio')
    layer1_ratio = st.sidebar.slider(
        "第一层买入比例",
        min_value=int(layer1_config['min_value'] * 100),
        max_value=int(layer1_config['max_value'] * 100),
        value=int(layer1_config['value'] * 100),
        step=int(layer1_config['step'] * 100),
        format="%.0f%%",
        help=layer1_config['help']
    ) / 100
    
    layer2_config = StrategyConfig.get_streamlit_config('layer2_ratio')
    layer2_ratio = st.sidebar.slider(
        "第二层买入比例", 
        min_value=int(layer2_config['min_value'] * 100),
        max_value=int(layer2_config['max_value'] * 100),
        value=int(layer2_config['value'] * 100),
        step=int(layer2_config['step'] * 100),
        format="%.0f%%",
        help=layer2_config['help']
    ) / 100
    
    layer3_config = StrategyConfig.get_streamlit_config('layer3_ratio')
    layer3_ratio = st.sidebar.slider(
        "第三层买入比例",
        min_value=int(layer3_config['min_value'] * 100),
        max_value=int(layer3_config['max_value'] * 100),
        value=int(layer3_config['value'] * 100),
        step=int(layer3_config['step'] * 100),
        format="%.0f%%",
        help=layer3_config['help']
    ) / 100
    
    # 分批止盈参数
    st.sidebar.subheader("💰 分批止盈设置")
    # 获取分批止盈参数配置
    partial_profit_config1 = StrategyConfig.get_param_config('partial_profit_multiplier1')
    partial_profit_config2 = StrategyConfig.get_param_config('partial_profit_multiplier2')
    partial_profit_config3 = StrategyConfig.get_param_config('partial_profit_multiplier3')
    
    partial_profit_multiplier1 = st.sidebar.slider(
        "第一次止盈倍数",
        min_value=partial_profit_config1.min_value,
        max_value=partial_profit_config1.max_value,
        value=float(partial_profit_config1.default_value),
        step=partial_profit_config1.step,
        format="%.1fx",
        help="相对于止盈目标的倍数"
    )
    
    partial_profit_multiplier2 = st.sidebar.slider(
        "第二次止盈倍数",
        min_value=partial_profit_config2.min_value,
        max_value=partial_profit_config2.max_value,
        value=float(partial_profit_config2.default_value),
        step=partial_profit_config2.step,
        format="%.1fx"
    )
    
    partial_profit_multiplier3 = st.sidebar.slider(
        "第三次止盈倍数",
        min_value=partial_profit_config3.min_value,
        max_value=partial_profit_config3.max_value,
        value=float(partial_profit_config3.default_value),
        step=partial_profit_config3.step,
        format="%.1fx"
    )
    
    # 获取分批卖出比例参数配置
    partial_sell_config1 = StrategyConfig.get_param_config('partial_sell_ratio1')
    partial_sell_config2 = StrategyConfig.get_param_config('partial_sell_ratio2')
    partial_sell_config3 = StrategyConfig.get_param_config('partial_sell_ratio3')
    
    partial_sell_ratio1 = st.sidebar.slider(
        "第一次卖出比例",
        min_value=partial_sell_config1.min_value * 100,
        max_value=partial_sell_config1.max_value * 100,
        value=partial_sell_config1.default_value * 100,
        step=partial_sell_config1.step * 100,
        format="%.0f%%"
    ) / 100
    
    partial_sell_ratio2 = st.sidebar.slider(
        "第二次卖出比例",
        min_value=partial_sell_config2.min_value * 100,
        max_value=partial_sell_config2.max_value * 100,
        value=partial_sell_config2.default_value * 100,
        step=partial_sell_config2.step * 100,
        format="%.0f%%"
    ) / 100
    
    partial_sell_ratio3 = st.sidebar.slider(
        "第三次卖出比例",
        min_value=partial_sell_config3.min_value * 100,
        max_value=partial_sell_config3.max_value * 100,
        value=partial_sell_config3.default_value * 100,
        step=partial_sell_config3.step * 100,
        format="%.0f%%"
    ) / 100
    
    # 风险控制参数
    st.sidebar.subheader("⚠️ 风险控制")
    
    # 添加是否收盘前平仓参数
    exit_at_close_config = StrategyConfig.get_param_config('exit_at_close')
    exit_at_close = st.sidebar.checkbox(
        "收盘前平仓",
        value=bool(exit_at_close_config.default_value),
        help=exit_at_close_config.description
    )
    
    # 获取风险控制参数配置
    daily_loss_config = StrategyConfig.get_param_config('daily_loss_limit')
    max_drawdown_config = StrategyConfig.get_param_config('max_drawdown_limit')
    
    daily_loss_limit = st.sidebar.slider(
        "日损失限制",
        min_value=daily_loss_config.min_value * 100,
        max_value=daily_loss_config.max_value * 100,
        value=daily_loss_config.default_value * 100,
        step=daily_loss_config.step * 100,
        format="%.0f%%",
        help="单日最大允许损失"
    ) / 100
    
    max_drawdown_limit = st.sidebar.slider(
        "最大回撤限制",
        min_value=max_drawdown_config.min_value * 100,
        max_value=max_drawdown_config.max_value * 100,
        value=max_drawdown_config.default_value * 100,
        step=max_drawdown_config.step * 100,
        format="%.0f%%",
        help="最大允许回撤"
    ) / 100
    
    # 运行回测按钮
    if st.sidebar.button("🚀 运行回测", type="primary"):
        # 创建配置
        # 创建配置
        config = BacktestConfig(
            symbol=symbol,
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d'),
            initial_capital=initial_capital,
            buy_trigger_drop=buy_trigger,
            profit_target=profit_target,
            stop_loss=stop_loss,
            max_hold_time=max_hold_time,
            commission_rate=commission_rate,
            slippage=slippage,
            # 新增的动态参数
            signal_window=signal_window,
            min_hold_time=min_hold_time,
            max_position=max_position,
            position_size=position_size,
            layer1_ratio=layer1_ratio,
            layer2_ratio=layer2_ratio,
            layer3_ratio=layer3_ratio,
            partial_profit_multiplier1=partial_profit_multiplier1,
            partial_profit_multiplier2=partial_profit_multiplier2,
            partial_profit_multiplier3=partial_profit_multiplier3,
            partial_sell_ratio1=partial_sell_ratio1,
            partial_sell_ratio2=partial_sell_ratio2,
            partial_sell_ratio3=partial_sell_ratio3,
            daily_loss_limit=daily_loss_limit,
            max_drawdown_limit=max_drawdown_limit,
            exit_at_close=1 if exit_at_close else 0
        )
        
        # 运行回测
        with st.spinner("正在运行回测..."):
            results = run_enhanced_backtest(config)
        
        if 'error' in results:
            st.error(f"回测失败: {results['error']}")
            return
        
        # 存储结果到session state
        st.session_state.backtest_results = results
        st.session_state.backtest_config = config
        st.success("回测完成！")
    
    # 显示波动分析结果
    if 'volatility_analysis' in st.session_state:
        st.markdown("---")
        st.markdown("## 📈 Tick数据波动分析结果")
        st.info("💡 以下分析结果可以帮助您优化策略参数设置")
        analysis = st.session_state.volatility_analysis
        
        # 创建四列显示分析结果
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.subheader("📊 数据概况")
            for key, value in analysis['数据概况'].items():
                st.metric(key, value)
        
        with col2:
            st.subheader("📈 波动分析")
            # 找到波动分析的键名
            volatility_key = None
            for key in analysis.keys():
                if 'tick窗口波动分析' in key:
                    volatility_key = key
                    break
            if volatility_key:
                for key, value in analysis[volatility_key].items():
                    st.metric(key, value)
        
        with col3:
            st.subheader("📉 回撤分析")
            # 找到回撤分析的键名
            drawdown_key = None
            for key in analysis.keys():
                if 'tick窗口回撤分析' in key:
                    drawdown_key = key
                    break
            if drawdown_key:
                for key, value in analysis[drawdown_key].items():
                    st.metric(key, value)
        
        with col4:
            st.subheader("💡 参数建议")
            if '策略参数建议' in analysis:
                for key, value in analysis['策略参数建议'].items():
                    st.metric(key, value)
        
        # 20tick窗口波动分布图
        if 'raw_data' in analysis:
            raw_data = analysis['raw_data']
            window_returns = raw_data['window_returns']
            window_drawdowns = raw_data['window_drawdowns']
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("20tick窗口收益率分布")
                fig_returns = px.histogram(
                    x=window_returns, nbins=50,
                    title="20tick窗口收益率分布（与策略信号一致）",
                    labels={'x': '20tick收益率', 'count': '频次'}
                )
                fig_returns.add_vline(
                    x=np.percentile(window_returns, 10), 
                    line_dash="dash", 
                    line_color="red",
                    annotation_text="建议买入触发点(10%分位)"
                )
                st.plotly_chart(fig_returns, use_container_width=True)
            
            with col2:
                st.subheader("20tick窗口回撤分布")
                fig_drawdown = px.histogram(
                    x=window_drawdowns, nbins=50,
                    title="20tick窗口回撤分布",
                    labels={'x': '20tick回撤', 'count': '频次'}
                )
                fig_drawdown.add_vline(
                    x=np.percentile(window_drawdowns, 5), 
                    line_dash="dash", 
                    line_color="red",
                    annotation_text="建议止损线(5%分位)"
                )
                st.plotly_chart(fig_drawdown, use_container_width=True)
        
        st.markdown("---")
    
    # 波动分析按钮
    if st.sidebar.button("📈 分析Tick波动", help="分析历史数据波动特征，为参数设置提供参考"):
        with st.spinner("正在分析tick数据波动..."):
            volatility_analysis = analyze_tick_volatility(
                symbol, 
                start_date.strftime('%Y-%m-%d'), 
                end_date.strftime('%Y-%m-%d')
            )
        
        if 'error' in volatility_analysis:
            st.error(f"波动分析失败: {volatility_analysis['error']}")
        else:
            st.session_state.volatility_analysis = volatility_analysis
            st.success("✅ 波动分析完成！请向下滚动查看分析结果")
            st.balloons()  # 添加庆祝动画提醒用户
    
    # 显示结果
    if 'backtest_results' in st.session_state:
        results = st.session_state.backtest_results
        config = st.session_state.backtest_config
        
        # 性能指标
        st.header("📊 回测结果")
        
        perf = results['performance']
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_return = float(perf['总收益率'].rstrip('%')) / 100
            color_class = "positive" if total_return > 0 else "negative" if total_return < 0 else "neutral"

            # 添加净值计算调试信息
            equity_df = results['raw_data']['equity_curve']
            if not equity_df.empty:
                initial_equity = float(perf['初始资金'].replace(',', ''))
                final_equity = float(perf['期末净值'].replace(',', ''))
                debug_return = (final_equity - initial_equity) / initial_equity * 100
                debug_info = f"(调试: {debug_return:.2f}%)"
            else:
                debug_info = "(调试: 无数据)"

            st.markdown(f"""
            <div class="metric-card blue">
                <h4>总收益率</h4>
                <h2 class="{color_class}">{perf['总收益率']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            max_dd = float(perf['最大回撤'].rstrip('%')) / 100
            color_class = "negative" if max_dd < -0.05 else "neutral"
            st.markdown(f"""
            <div class="metric-card green">
                <h4>最大回撤</h4>
                <h2 class="{color_class}">{perf['最大回撤']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            win_rate = float(perf['胜率'].rstrip('%')) / 100
            color_class = "positive" if win_rate > 0.6 else "neutral"

            # 添加调试信息
            trades_df = results['raw_data']['trades']
            sell_trades = trades_df[trades_df['type'] == 'SELL'] if not trades_df.empty else pd.DataFrame()
            # debug_info = f"(调试: {len(sell_trades)}笔卖出)" if not sell_trades.empty else "(调试: 无卖出交易)"

            st.markdown(f"""
            <div class="metric-card orange">
                <h4>胜率</h4>
                <h2 class="{color_class}">{perf['胜率']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            st.markdown(f"""
            <div class="metric-card purple">
                <h4>交易次数</h4>
                <h2 class="neutral">{perf['总交易次数']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # 详细指标
        st.subheader("📈 详细指标")
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**财务指标**")
            st.write(f"初始资金: {perf['初始资金']}")
            st.write(f"期末净值: {perf['期末净值']}")
            st.write(f"夏普比率: {perf['夏普比率']}")
            st.write(f"总手续费: {perf['总手续费']}")

            # 添加净值分解信息
            equity_df = results['raw_data']['equity_curve']
            trades_df = results['raw_data']['trades']
            if not equity_df.empty and not trades_df.empty:
                final_row = equity_df.iloc[-1]
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                total_realized_pnl = sell_trades['pnl'].sum() if not sell_trades.empty else 0

                st.write("**💰 净值分解**")
                st.write(f"已实现盈亏: {total_realized_pnl:.2f}元")
                if 'cash' in final_row:
                    st.write(f"现金余额: {final_row['cash']:.2f}元")
                if 'market_value' in final_row:
                    st.write(f"持仓市值: {final_row['market_value']:.2f}元")
        
        with col2:
            st.write("**交易统计**")
            st.write(f"买入次数: {perf['买入次数']}")
            st.write(f"卖出次数: {perf['卖出次数']}")
            st.write(f"胜率: {perf['胜率']}")

            # 添加详细的胜率调试信息
            trades_df = results['raw_data']['trades']
            if not trades_df.empty:
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                if not sell_trades.empty and 'pnl' in sell_trades.columns:
                    profitable = len(sell_trades[sell_trades['pnl'] > 0])
                    losing = len(sell_trades[sell_trades['pnl'] < 0])
                    st.write(f"📊 胜率详情: {profitable}胜/{losing}负 (共{len(sell_trades)}笔)")

                    # 显示PnL范围
                    pnl_min = sell_trades['pnl'].min()
                    pnl_max = sell_trades['pnl'].max()
                    st.write(f"💰 盈亏范围: {pnl_min:.2f} ~ {pnl_max:.2f}")
                else:
                    st.write("⚠️ 无卖出交易或缺少PnL数据")
        
        # 图表展示
        raw_data = results['raw_data']
        
        if not raw_data['signals'].empty:
            st.subheader("📊 策略执行详情")
            fig1 = create_price_and_signals_chart(
                raw_data['signals'], 
                raw_data['trades'],
                config
            )
            st.plotly_chart(fig1, use_container_width=True)
        
        if not raw_data['equity_curve'].empty:
            st.subheader("💰 净值表现")
            fig2 = create_equity_curve_chart(raw_data['equity_curve'], config)
            st.plotly_chart(fig2, use_container_width=True)
        
        if not raw_data['trades'].empty:
            st.subheader("🔍 交易分析")
            fig3 = create_trade_analysis_chart(raw_data['trades'])
            st.plotly_chart(fig3, width='stretch')
            
            # 交易明细表
            st.subheader("📋 交易明细")

            # 添加导出按钮
            col1, col2 = st.columns([3, 1])
            with col2:
                if st.button("📥 导出CSV"):
                    # 生成带时间戳的文件名
                    timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M")
                    csv_filename = f"{timestamp}_export.csv"

                    # 导出完整的交易数据
                    raw_data['trades'].to_csv(csv_filename, index=True)
                    st.success(f"交易数据已导出到: {csv_filename}")

                    # 显示导出统计
                    trades_df = raw_data['trades']
                    sell_trades = trades_df[trades_df['type'] == 'SELL']
                    if not sell_trades.empty and 'pnl' in sell_trades.columns:
                        profitable = len(sell_trades[sell_trades['pnl'] > 0])
                        total_sell = len(sell_trades)
                        export_win_rate = profitable / total_sell if total_sell > 0 else 0
                        st.info(f"导出数据统计: {total_sell}笔卖出交易，胜率: {export_win_rate:.2%}")

            with col1:
                st.dataframe(
                    raw_data['trades'].style.format({
                        'price': '{:.4f}',
                        'actual_price': '{:.4f}',
                        'pnl': '{:.2f}'
                    }),
                    width='stretch'
                )
    
    else:
        st.info("👈 请在左侧配置参数并点击'运行回测'开始分析")
        
        # 显示功能介绍
        st.header("🌟 功能特色")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            **🎯 精准回测**
            - 基于真实tick数据
            - 考虑滑点和手续费
            - 完整的风险控制
            """)
        
        with col2:
            st.markdown("""
            **📊 直观展示**
            - 实时价格走势
            - 交易信号可视化
            - 净值曲线分析
            """)
        
        with col3:
            st.markdown("""
            **⚡ 智能策略**
            - 分层买入机制
            - 分批止盈策略
            - 动态风险管理
            """)

if __name__ == "__main__":
    main()