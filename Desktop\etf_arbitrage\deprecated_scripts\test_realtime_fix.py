#!/usr/bin/env python3
"""
测试实时监控页面修复
"""

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        import app_enhanced_realtime_dashboard
        from app_enhanced_realtime_dashboard import RealtimeSimulator
        from strategy_config import StrategyConfig
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_config():
    """测试配置系统"""
    try:
        print("测试配置系统...")
        from strategy_config import StrategyConfig
        
        # 测试获取默认值
        defaults = StrategyConfig.get_default_values()
        print(f"✅ 默认配置获取成功，参数数量: {len(defaults)}")
        
        # 测试预设配置
        preset = StrategyConfig.get_preset_config("平衡型")
        print(f"✅ 预设配置获取成功: {preset['buy_trigger_drop']}")
        
        # 测试验证
        errors = StrategyConfig.validate_config(defaults)
        if not errors:
            print("✅ 配置验证通过")
        else:
            print(f"❌ 配置验证失败: {errors}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_simulator():
    """测试模拟器"""
    try:
        print("测试模拟器...")
        from app_enhanced_realtime_dashboard import RealtimeSimulator
        from strategy_config import StrategyConfig
        
        # 创建模拟器
        simulator = RealtimeSimulator()
        print("✅ 模拟器创建成功")
        
        # 测试配置初始化
        config_dict = StrategyConfig.get_default_values()
        initial_position = {'quantity': 0, 'avg_cost': 0.0}
        
        simulator.initialize_strategy(config_dict, initial_position, 1000000.0)
        print("✅ 策略初始化成功")
        
        # 检查配置属性
        if hasattr(simulator.config, 'buy_trigger_drop'):
            print(f"✅ 配置属性正确: buy_trigger_drop = {simulator.config.buy_trigger_drop}")
        else:
            print("❌ 配置属性缺失")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 模拟器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== 实时监控页面修复测试 ===")
    
    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_config),
        ("模拟器测试", test_simulator)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        result = test_func()
        results.append((name, result))
    
    print("\n=== 测试结果汇总 ===")
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！实时监控页面修复成功！")
        print("可以使用以下命令启动:")
        print("streamlit run app_enhanced_realtime_dashboard.py")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
    
    return all_passed

if __name__ == "__main__":
    main()