#!/usr/bin/env python3
"""
简化的交易分析
"""

from backtest_enhanced import BacktestConfig, EnhancedBacktest
import logging

def simple_analysis():
    """简化分析"""
    
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    config = BacktestConfig()
    backtest = EnhancedBacktest(config)
    results = backtest.run_backtest()
    
    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return
    
    trades_df = results['raw_data']['trades']
    
    print("=== 交易记录 ===")
    for idx, trade in trades_df.iterrows():
        if trade['type'] == 'SELL':
            print(f"卖出 {idx}: {trade['time']} - {trade['reason']}")
            
            # 检查是否是错误的止损
            if '止损' in trade['reason'] and '0.02%' in trade['reason']:
                print(f"  ⚠️  发现问题: {trade['reason']}")
                print(f"  配置止损线: {config.stop_loss:.2%}")

if __name__ == "__main__":
    simple_analysis()