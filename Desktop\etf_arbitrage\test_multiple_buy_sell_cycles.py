#!/usr/bin/env python3
"""
测试多次买卖循环中的潜在问题
模拟复杂的买入-卖出-再买入-再卖出场景
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from datetime import datetime, timedelta
from strategy_engine_enhanced import Position
import pandas as pd

def test_multiple_cycles():
    """测试多次买卖循环"""
    print("=== 多次买卖循环测试 ===")
    
    position = Position()
    transactions = []
    
    # 模拟复杂的交易场景
    scenarios = [
        # (action, quantity, price, description)
        ("BUY", 3000, 10.00, "初始买入1"),
        ("BUY", 3000, 9.95, "初始买入2"),
        ("BUY", 4000, 9.90, "初始买入3"),
        ("SELL", 3000, 10.03, "第一次止盈30%"),  # 应该卖出30%
        ("BUY", 2000, 9.88, "价格下跌后加仓"),
        ("SELL", 4000, 10.06, "第二次止盈40%"),  # 应该卖出40%
        ("BUY", 3000, 9.85, "继续下跌加仓"),
        ("SELL", 7000, 9.80, "止损全部清仓"),   # 止损
        ("BUY", 5000, 9.75, "重新建仓"),
        ("SELL", 1500, 9.78, "小幅止盈30%"),
    ]
    
    for i, (action, quantity, price, description) in enumerate(scenarios):
        print(f"\n步骤 {i+1}: {description}")
        print(f"操作: {action} {quantity}股 @ {price:.2f}")
        
        if action == "BUY":
            position.add_position(quantity, price, datetime.now())
            transactions.append({
                'step': i+1,
                'action': action,
                'quantity': quantity,
                'price': price,
                'description': description
            })
        elif action == "SELL":
            if position.total_quantity > 0:
                cost_reduced = position.reduce_position(quantity)
                transactions.append({
                    'step': i+1,
                    'action': action,
                    'quantity': quantity,
                    'price': price,
                    'cost_reduced': cost_reduced,
                    'description': description
                })
        
        # 显示当前状态
        print(f"当前仓位: {position.total_quantity}股")
        if position.total_quantity > 0:
            avg_cost = position.total_cost / position.total_quantity
            profit_rate = position.get_profit_rate(price)
            print(f"平均成本: {avg_cost:.4f}")
            print(f"当前收益率: {profit_rate:.2%}")
            print(f"批次数量: {len(position.batches)}")
        else:
            print("无持仓")
        print(f"止盈级别: {position.profit_level_reached}")
        print("-" * 60)
    
    return transactions

def test_profit_level_reset_issue():
    """测试止盈级别重置的问题"""
    print("\n=== 止盈级别重置问题测试 ===")
    
    position = Position()
    
    # 场景1: 买入 -> 分批止盈 -> 再买入 -> 止盈级别是否正确重置
    print("场景1: 分批止盈后再买入")
    
    # 初始买入
    position.add_position(10000, 10.00, datetime.now())
    print(f"1. 买入10000股@10.00, 止盈级别: {position.profit_level_reached}")
    
    # 第一次止盈
    should_sell, level, ratio, reason = position.check_partial_sell(10.03)  # 0.3%收益
    if should_sell:
        print(f"2. 触发止盈: {reason}, 卖出比例: {ratio:.1%}, 新级别: {position.profit_level_reached}")
    
    # 再次买入（这里是关键问题点）
    position.add_position(5000, 9.95, datetime.now())
    print(f"3. 再次买入5000股@9.95, 止盈级别: {position.profit_level_reached}")
    
    # 检查止盈级别是否应该重置
    current_profit = position.get_profit_rate(10.03)
    print(f"4. 当前整体收益率: {current_profit:.2%}")
    
    # 再次检查止盈
    should_sell, level, ratio, reason = position.check_partial_sell(10.03)
    print(f"5. 再次检查止盈: 是否卖出={should_sell}, 原因={reason}")

def test_fifo_cost_calculation():
    """测试FIFO成本计算的准确性"""
    print("\n=== FIFO成本计算测试 ===")
    
    position = Position()
    
    # 多次不同价格买入
    buys = [(3000, 10.00), (3000, 9.95), (4000, 9.90)]
    for qty, price in buys:
        position.add_position(qty, price, datetime.now())
        print(f"买入: {qty}股@{price:.2f}")
    
    print(f"总仓位: {position.total_quantity}股")
    print(f"总成本: {position.total_cost:.2f}")
    print(f"平均成本: {position.total_cost/position.total_quantity:.4f}")
    
    # 分批卖出，验证FIFO逻辑
    sells = [2000, 3000, 2000]
    for sell_qty in sells:
        if position.total_quantity >= sell_qty:
            cost_reduced = position.reduce_position(sell_qty)
            remaining_avg = position.total_cost / position.total_quantity if position.total_quantity > 0 else 0
            print(f"卖出: {sell_qty}股, 减少成本: {cost_reduced:.2f}, 剩余平均成本: {remaining_avg:.4f}")
        else:
            print(f"无法卖出{sell_qty}股，仓位不足")

def test_time_tracking_issue():
    """测试持仓时间跟踪问题"""
    print("\n=== 持仓时间跟踪测试 ===")
    
    position = Position()
    
    # 初始买入
    start_time = datetime.now()
    position.add_position(5000, 10.00, start_time)
    print(f"1. 初始买入时间: {start_time}")
    print(f"   首次买入时间: {position.first_buy_time}")
    
    # 等待一段时间后再买入
    second_time = start_time + timedelta(minutes=30)
    position.add_position(3000, 9.95, second_time)
    print(f"2. 第二次买入时间: {second_time}")
    print(f"   首次买入时间: {position.first_buy_time}")  # 应该保持不变
    
    # 部分卖出
    position.reduce_position(2000)
    print(f"3. 部分卖出后首次买入时间: {position.first_buy_time}")
    
    # 全部卖出
    position.reduce_position(position.total_quantity)
    print(f"4. 全部卖出后首次买入时间: {position.first_buy_time}")  # 应该重置为None
    
    # 重新买入
    new_start = datetime.now()
    position.add_position(4000, 9.80, new_start)
    print(f"5. 重新买入时间: {new_start}")
    print(f"   新的首次买入时间: {position.first_buy_time}")

def analyze_potential_issues():
    """分析潜在问题"""
    print("\n=== 潜在问题分析 ===")
    
    issues = [
        {
            "问题": "止盈级别重置时机",
            "描述": "分批卖出后再买入，止盈级别是否应该重置？",
            "当前行为": "只有在买入前手动调用reset_partial_sold_flags()才重置",
            "潜在风险": "新买入的仓位可能无法享受完整的分批止盈"
        },
        {
            "问题": "FIFO成本计算",
            "描述": "多次买卖后，成本计算是否准确？",
            "当前行为": "按照先进先出原则减少成本",
            "潜在风险": "复杂场景下可能出现成本计算偏差"
        },
        {
            "问题": "持仓时间跟踪",
            "描述": "部分卖出后，持仓时间如何计算？",
            "当前行为": "基于first_buy_time计算",
            "潜在风险": "可能不准确反映实际持仓时间"
        },
        {
            "问题": "批次管理复杂性",
            "描述": "多次买卖后批次数量增加，管理复杂",
            "当前行为": "每次买入创建新批次",
            "潜在风险": "内存占用增加，逻辑复杂化"
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"{i}. {issue['问题']}")
        print(f"   描述: {issue['描述']}")
        print(f"   当前行为: {issue['当前行为']}")
        print(f"   潜在风险: {issue['潜在风险']}")
        print()

if __name__ == "__main__":
    test_multiple_cycles()
    test_profit_level_reset_issue()
    test_fifo_cost_calculation()
    test_time_tracking_issue()
    analyze_potential_issues()
