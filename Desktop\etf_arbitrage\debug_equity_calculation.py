#!/usr/bin/env python3
"""
调试净值计算逻辑，分析为什么总收益率是负的
"""

import pandas as pd
import numpy as np

def debug_equity_calculation():
    """调试净值计算"""
    
    print("=== 调试净值计算逻辑 ===")
    
    # 读取交易数据
    csv_file = '2025-09-01T16-54_export.csv'
    df = pd.read_csv(csv_file)
    
    print(f"CSV文件: {csv_file}")
    print(f"总交易数: {len(df)}")
    
    # 初始参数
    initial_capital = 1000000  # 100万初始资金
    commission_rate = 0.0003   # 万分之三手续费
    
    print(f"\n📊 初始参数:")
    print(f"初始资金: {initial_capital:,}元")
    print(f"手续费率: {commission_rate:.4f}")
    
    # 模拟净值计算过程
    current_equity = initial_capital
    total_cost = 0  # 总投入成本
    total_quantity = 0  # 总持仓数量
    total_commission = 0  # 总手续费
    total_realized_pnl = 0  # 总已实现盈亏
    
    equity_curve = []
    
    print(f"\n📈 逐笔交易分析:")
    print("=" * 80)
    
    for i, row in df.iterrows():
        trade_type = row['type']
        quantity = row['quantity']
        price = row['price']
        actual_price = row['actual_price']
        pnl = row['pnl'] if pd.notna(row['pnl']) else 0
        
        # 计算手续费
        trade_value = quantity * actual_price
        commission = trade_value * commission_rate
        total_commission += commission
        
        if trade_type == 'BUY':
            # 买入：增加持仓，增加成本
            total_quantity += quantity
            cost = quantity * actual_price + commission
            total_cost += cost
            
            # 净值计算：初始资金 - 已投入成本 + 当前市值 - 手续费
            market_value = total_quantity * actual_price
            current_equity = initial_capital - total_cost + market_value
            
            print(f"第{i+1}笔 BUY: 数量={quantity:,}, 价格={actual_price:.4f}, 手续费={commission:.2f}")
            print(f"  累计持仓: {total_quantity:,}, 累计成本: {total_cost:.2f}")
            print(f"  市值: {market_value:.2f}, 净值: {current_equity:.2f}")
            
        elif trade_type == 'SELL':
            # 卖出：减少持仓，实现盈亏
            total_quantity -= quantity
            
            # 计算卖出成本（按比例）
            if total_quantity + quantity > 0:  # 避免除零
                avg_cost_per_share = total_cost / (total_quantity + quantity)
                cost_reduced = quantity * avg_cost_per_share
                total_cost -= cost_reduced
            else:
                cost_reduced = total_cost
                total_cost = 0
            
            # 实现盈亏
            realized_pnl = pnl  # CSV中已经计算好的PnL
            total_realized_pnl += realized_pnl
            
            # 净值计算
            if total_quantity > 0:
                market_value = total_quantity * actual_price
                current_equity = initial_capital - total_cost + market_value
            else:
                # 没有持仓时，净值 = 初始资金 + 已实现盈亏 - 总手续费
                current_equity = initial_capital + total_realized_pnl - total_commission
            
            print(f"第{i+1}笔 SELL: 数量={quantity:,}, 价格={actual_price:.4f}, PnL={realized_pnl:.2f}")
            print(f"  剩余持仓: {total_quantity:,}, 剩余成本: {total_cost:.2f}")
            print(f"  累计已实现PnL: {total_realized_pnl:.2f}, 净值: {current_equity:.2f}")
        
        # 记录净值曲线
        equity_curve.append({
            'trade_index': i + 1,
            'type': trade_type,
            'equity': current_equity,
            'total_quantity': total_quantity,
            'total_cost': total_cost,
            'total_commission': total_commission,
            'total_realized_pnl': total_realized_pnl
        })
        
        print("-" * 80)
    
    # 最终分析
    final_equity = current_equity
    total_return = (final_equity - initial_capital) / initial_capital
    
    print(f"\n📊 最终结果:")
    print(f"初始资金: {initial_capital:,}元")
    print(f"期末净值: {final_equity:.2f}元")
    print(f"总收益率: {total_return:.2%}")
    print(f"总手续费: {total_commission:.2f}元")
    print(f"总已实现盈亏: {total_realized_pnl:.2f}元")
    
    # 分析问题
    print(f"\n🔍 问题分析:")
    
    # 1. 检查是否还有持仓
    if total_quantity > 0:
        print(f"1. ⚠️ 回测结束时仍有持仓: {total_quantity:,}股")
        print(f"   持仓成本: {total_cost:.2f}元")
        
        # 获取最后价格
        last_price = df.iloc[-1]['actual_price']
        unrealized_market_value = total_quantity * last_price
        unrealized_pnl = unrealized_market_value - total_cost
        
        print(f"   最后价格: {last_price:.4f}")
        print(f"   未实现市值: {unrealized_market_value:.2f}元")
        print(f"   未实现盈亏: {unrealized_pnl:.2f}元")
        
        # 如果全部平仓的话
        total_if_liquidated = initial_capital + total_realized_pnl + unrealized_pnl - total_commission
        return_if_liquidated = (total_if_liquidated - initial_capital) / initial_capital
        
        print(f"   如果全部平仓净值: {total_if_liquidated:.2f}元")
        print(f"   如果全部平仓收益率: {return_if_liquidated:.2%}")
    else:
        print("1. ✅ 回测结束时无持仓")
    
    # 2. 手续费影响
    print(f"2. 手续费影响: -{total_commission:.2f}元 ({-total_commission/initial_capital:.2%})")
    
    # 3. 已实现vs未实现盈亏
    if total_quantity > 0:
        last_price = df.iloc[-1]['actual_price']
        unrealized_pnl = (total_quantity * last_price) - total_cost
        print(f"3. 已实现盈亏: {total_realized_pnl:.2f}元")
        print(f"   未实现盈亏: {unrealized_pnl:.2f}元")
        print(f"   总盈亏: {total_realized_pnl + unrealized_pnl:.2f}元")
    
    # 4. 净值计算验证
    print(f"\n🧮 净值计算验证:")
    if total_quantity > 0:
        last_price = df.iloc[-1]['actual_price']
        market_value = total_quantity * last_price
        calculated_equity = initial_capital - total_cost + market_value - total_commission
        print(f"计算公式: {initial_capital} - {total_cost:.2f} + {market_value:.2f} - {total_commission:.2f} = {calculated_equity:.2f}")
    else:
        calculated_equity = initial_capital + total_realized_pnl - total_commission
        print(f"计算公式: {initial_capital} + {total_realized_pnl:.2f} - {total_commission:.2f} = {calculated_equity:.2f}")
    
    print(f"实际净值: {final_equity:.2f}")
    print(f"计算差异: {abs(calculated_equity - final_equity):.2f}")
    
    # 5. 可能的问题
    print(f"\n❓ 可能的问题:")
    if total_return < 0 and total_realized_pnl > 0:
        print("1. 总收益率为负，但已实现盈亏为正 → 可能是手续费或未实现亏损导致")
        
        if total_commission > total_realized_pnl:
            print("2. 手续费超过已实现盈亏 → 这是主要原因")
        
        if total_quantity > 0:
            print("3. 仍有持仓未平仓 → 可能存在未实现亏损")
    
    return equity_curve

if __name__ == "__main__":
    debug_equity_calculation()
