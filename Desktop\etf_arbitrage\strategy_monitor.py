"""
策略性能监控和分析工具
实时监控策略表现，生成性能报告
"""

import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import json

plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False


class StrategyMonitor:
    """策略监控器"""
    
    def __init__(self, db_path: str = "ticks.db"):
        self.db_path = db_path
        self.reports_dir = Path("strategy_reports")
        self.reports_dir.mkdir(exist_ok=True)
    
    def load_signals(self, symbol: str, days: int = 7) -> pd.DataFrame:
        """加载策略信号数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            since_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            query = """
            SELECT symbol, ts, signal, quantity, price, reason
            FROM strategy_signals 
            WHERE symbol=? AND ts>=? 
            ORDER BY ts ASC
            """
            
            df = pd.read_sql_query(query, conn, params=[symbol, since_date])
            conn.close()
            
            if not df.empty:
                df['ts'] = pd.to_datetime(df['ts'])
                df['quantity'] = pd.to_numeric(df['quantity'], errors='coerce').fillna(0)
                df['price'] = pd.to_numeric(df['price'], errors='coerce').fillna(0)
            
            return df
        except Exception as e:
            print(f"加载信号数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_performance_metrics(self, signals_df: pd.DataFrame, 
                                    initial_capital: float = 1_000_000.0) -> Dict:
        """计算策略性能指标"""
        if signals_df.empty:
            return {}
        
        # 模拟交易过程
        cash = initial_capital
        position = 0
        trades = []
        equity_curve = []
        
        for _, row in signals_df.iterrows():
            if row['signal'] == 'B':  # 买入
                cost = row['quantity'] * row['price']
                if cash >= cost:
                    cash -= cost
                    position += row['quantity']
                    trades.append({
                        'type': 'BUY',
                        'time': row['ts'],
                        'quantity': row['quantity'],
                        'price': row['price'],
                        'reason': row['reason']
                    })
            
            elif row['signal'] == 'S':  # 卖出
                if position >= row['quantity']:
                    proceeds = row['quantity'] * row['price']
                    cash += proceeds
                    position -= row['quantity']
                    trades.append({
                        'type': 'SELL',
                        'time': row['ts'],
                        'quantity': row['quantity'],
                        'price': row['price'],
                        'reason': row['reason']
                    })
            
            # 记录净值（假设当前价格等于最后交易价格）
            market_value = position * row['price']
            total_equity = cash + market_value
            equity_curve.append({
                'time': row['ts'],
                'equity': total_equity,
                'cash': cash,
                'position': position,
                'market_value': market_value
            })
        
        if not equity_curve:
            return {}
        
        # 计算性能指标
        equity_df = pd.DataFrame(equity_curve)
        final_equity = equity_df['equity'].iloc[-1]
        
        # 收益率
        total_return = (final_equity - initial_capital) / initial_capital
        
        # 最大回撤
        peak = equity_df['equity'].cummax()
        drawdown = (equity_df['equity'] - peak) / peak
        max_drawdown = drawdown.min()
        
        # 交易统计
        buy_trades = [t for t in trades if t['type'] == 'BUY']
        sell_trades = [t for t in trades if t['type'] == 'SELL']
        
        # 胜率计算（简化版）
        profitable_trades = 0
        total_completed_trades = 0
        
        # 配对买卖交易计算盈亏
        buy_queue = []
        for trade in trades:
            if trade['type'] == 'BUY':
                buy_queue.append(trade)
            elif trade['type'] == 'SELL' and buy_queue:
                buy_trade = buy_queue.pop(0)  # FIFO
                profit = (trade['price'] - buy_trade['price']) * trade['quantity']
                if profit > 0:
                    profitable_trades += 1
                total_completed_trades += 1
        
        win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
        
        return {
            'initial_capital': initial_capital,
            'final_equity': final_equity,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': len(trades),
            'buy_trades': len(buy_trades),
            'sell_trades': len(sell_trades),
            'win_rate': win_rate,
            'equity_curve': equity_df,
            'trades': trades
        }
    
    def generate_performance_report(self, symbol: str, days: int = 7) -> Dict:
        """生成性能报告"""
        signals_df = self.load_signals(symbol, days)
        if signals_df.empty:
            return {'error': '没有找到策略信号数据'}
        
        metrics = self.calculate_performance_metrics(signals_df)
        if not metrics:
            return {'error': '无法计算性能指标'}
        
        # 生成报告
        report = {
            'symbol': symbol,
            'period': f'{days}天',
            'report_time': datetime.now().isoformat(),
            'performance': {
                '初始资金': f"{metrics['initial_capital']:,.2f}",
                '期末净值': f"{metrics['final_equity']:,.2f}",
                '总收益率': f"{metrics['total_return']:.2%}",
                '最大回撤': f"{metrics['max_drawdown']:.2%}",
                '总交易次数': metrics['total_trades'],
                '买入次数': metrics['buy_trades'],
                '卖出次数': metrics['sell_trades'],
                '胜率': f"{metrics['win_rate']:.2%}"
            },
            'raw_metrics': metrics
        }
        
        return report
    
    def generate_full_report(self, symbol: str, days: int = 7):
        """生成完整报告"""
        print(f"正在生成 {symbol} 的策略性能报告...")
        
        # 生成性能报告
        report = self.generate_performance_report(symbol, days)
        if 'error' in report:
            print(f"报告生成失败: {report['error']}")
            return
        
        # 打印报告摘要
        print("\n=== 策略性能报告 ===")
        print(f"标的: {report['symbol']}")
        print(f"统计周期: {report['period']}")
        print("\n性能指标:")
        for key, value in report['performance'].items():
            print(f"  {key}: {value}")
        
        return report


def main():
    """主函数示例"""
    import argparse
    
    parser = argparse.ArgumentParser(description="策略性能监控工具")
    parser.add_argument("--symbol", required=True, help="交易标的")
    parser.add_argument("--days", type=int, default=7, help="统计天数")
    parser.add_argument("--db-path", default="ticks.db", help="数据库路径")
    
    args = parser.parse_args()
    
    monitor = StrategyMonitor(args.db_path)
    monitor.generate_full_report(args.symbol, args.days)


if __name__ == "__main__":
    main()