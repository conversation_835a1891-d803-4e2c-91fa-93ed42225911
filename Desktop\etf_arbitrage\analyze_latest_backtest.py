#!/usr/bin/env python3
"""
分析最新回测结果 - 胜率为0的问题分析
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_latest_backtest():
    """分析最新回测结果"""
    
    # 读取最新CSV数据
    df = pd.read_csv('2025-09-01T03-08_export.csv')
    
    print("=== 最新回测结果深度分析 ===")
    print(f"回测期间: 2025-08-26, 2025-08-28")
    print(f"总交易次数: {len(df)}笔")
    
    # 基本统计
    total_pnl = df['pnl'].sum()
    print(f"\n💰 总盈亏: {total_pnl:.2f}元")
    
    # 分析买卖交易
    buy_trades = df[df['type'] == 'BUY']
    sell_trades = df[df['type'] == 'SELL']
    
    print(f"\n📊 交易结构:")
    print(f"买入交易: {len(buy_trades)}笔")
    print(f"卖出交易: {len(sell_trades)}笔")
    print(f"买入总量: {buy_trades['quantity'].sum():,}股")
    print(f"卖出总量: {sell_trades['quantity'].sum():,}股")
    
    # 分析盈亏交易
    profit_trades = df[df['pnl'] > 0]
    loss_trades = df[df['pnl'] < 0]
    zero_trades = df[df['pnl'] == 0]
    
    print(f"\n📈 盈亏分析:")
    print(f"盈利交易: {len(profit_trades)}笔, 总盈利: {profit_trades['pnl'].sum():.2f}元")
    print(f"亏损交易: {len(loss_trades)}笔, 总亏损: {loss_trades['pnl'].sum():.2f}元")
    print(f"无盈亏交易(买入): {len(zero_trades)}笔")
    
    # 胜率计算
    valid_trades = len(df[df['pnl'] != 0])  # 只计算有盈亏的交易
    if valid_trades > 0:
        win_rate = len(profit_trades) / valid_trades * 100
        print(f"胜率: {win_rate:.1f}%")
        print(f"有效交易数: {valid_trades}笔")
    else:
        print("胜率: 无法计算 (无有效交易)")
    
    # 分析交易原因
    print(f"\n🎯 交易原因详细分析:")
    reasons = df['reason'].value_counts()
    for reason, count in reasons.items():
        if pd.notna(reason):
            reason_trades = df[df['reason'] == reason]
            reason_pnl = reason_trades['pnl'].sum()
            avg_pnl = reason_pnl / count if count > 0 else 0
            print(f"{reason}: {count}笔, 总盈亏: {reason_pnl:.2f}元, 平均: {avg_pnl:.2f}元")
    
    # 按日期分析
    print(f"\n📅 按日期分析:")
    df['date'] = pd.to_datetime(df['time']).dt.date
    daily_analysis = df.groupby('date').agg({
        'pnl': ['count', 'sum'],
        'quantity': 'sum'
    }).round(2)
    
    for date in df['date'].unique():
        date_data = df[df['date'] == date]
        trades_count = len(date_data)
        date_pnl = date_data['pnl'].sum()
        buy_count = len(date_data[date_data['type'] == 'BUY'])
        sell_count = len(date_data[date_data['type'] == 'SELL'])
        print(f"{date}: {trades_count}笔交易 (买{buy_count}/卖{sell_count}), 盈亏: {date_pnl:.2f}元")
    
    # 价格分析
    print(f"\n💹 价格区间分析:")
    print(f"最高交易价格: {df['price'].max():.3f}")
    print(f"最低交易价格: {df['price'].min():.3f}")
    print(f"价格波动幅度: {(df['price'].max() - df['price'].min()) / df['price'].min() * 100:.2f}%")
    
    # 仓位分析
    print(f"\n📦 仓位变化分析:")
    position = 0
    max_position = 0
    min_position = 0
    
    for _, row in df.iterrows():
        if row['type'] == 'BUY':
            position += row['quantity']
        else:
            position -= row['quantity']
        max_position = max(max_position, position)
        min_position = min(min_position, position)
    
    print(f"最大持仓: {max_position:,}股")
    print(f"最小持仓: {min_position:,}股")
    print(f"最终持仓: {position:,}股")
    
    # 问题诊断
    print(f"\n🔍 问题诊断:")
    
    # 1. 胜率为0的原因
    if len(profit_trades) == 0:
        print("❌ 胜率为0原因: 没有任何盈利交易")
    elif valid_trades == 0:
        print("❌ 胜率为0原因: 没有有效交易(所有交易盈亏为0)")
    else:
        print(f"✅ 胜率正常: {win_rate:.1f}%")
    
    # 2. 收益为负的原因
    if total_pnl < 0:
        print(f"❌ 收益为负原因分析:")
        if len(loss_trades) > 0:
            print(f"   - 亏损交易: {len(loss_trades)}笔, 总亏损: {loss_trades['pnl'].sum():.2f}元")
        if len(profit_trades) > 0:
            print(f"   - 盈利交易: {len(profit_trades)}笔, 总盈利: {profit_trades['pnl'].sum():.2f}元")
            print(f"   - 盈亏比: {abs(loss_trades['pnl'].sum() / profit_trades['pnl'].sum()):.2f}")
    
    # 3. 交易逻辑问题
    if len(buy_trades) != len(sell_trades):
        print(f"⚠️ 交易不平衡: 买入{len(buy_trades)}笔 vs 卖出{len(sell_trades)}笔")
    
    # 4. 仓位问题
    if position != 0:
        print(f"⚠️ 仓位未清零: 剩余{position:,}股")
    
    # 5. 分析止盈止损效果
    profit_taking_trades = df[df['reason'].str.contains('止盈', na=False)]
    stop_loss_trades = df[df['reason'].str.contains('止损', na=False)]
    close_trades = df[df['reason'].str.contains('收盘', na=False)]
    
    print(f"\n📊 策略效果分析:")
    if len(profit_taking_trades) > 0:
        print(f"止盈交易: {len(profit_taking_trades)}笔, 盈利: {profit_taking_trades['pnl'].sum():.2f}元")
    if len(stop_loss_trades) > 0:
        print(f"止损交易: {len(stop_loss_trades)}笔, 亏损: {stop_loss_trades['pnl'].sum():.2f}元")
    if len(close_trades) > 0:
        print(f"收盘平仓: {len(close_trades)}笔, 盈亏: {close_trades['pnl'].sum():.2f}元")
    
    # 计算理论收益率
    initial_capital = 1000000  # 100万初始资金
    return_rate = total_pnl / initial_capital * 100
    print(f"\n📈 收益率计算:")
    print(f"初始资金: {initial_capital:,}元")
    print(f"总盈亏: {total_pnl:.2f}元")
    print(f"收益率: {return_rate:.2f}%")
    
    # 手续费估算
    total_volume = df['quantity'].sum() * df['price'].mean()
    estimated_fees = total_volume * 0.0003  # 万分之三手续费
    print(f"预估手续费: {estimated_fees:.2f}元")

if __name__ == "__main__":
    analyze_latest_backtest()
