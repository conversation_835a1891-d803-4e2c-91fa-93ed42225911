"""
统一配置使用示例
展示如何在回测和实时监控界面中使用相同的参数配置
"""

import streamlit as st
from strategy_config import StrategyConfig, STRATEGY_PRESETS

def create_unified_parameter_ui():
    """创建统一的参数配置界面"""
    
    st.sidebar.header("📊 策略参数配置")
    
    # 预设配置选择
    preset_options = ["自定义"] + list(STRATEGY_PRESETS.keys())
    config_preset = st.sidebar.selectbox(
        "预设配置",
        preset_options,
        format_func=lambda x: STRATEGY_PRESETS[x]['name'] if x in STRATEGY_PRESETS else x
    )
    
    # 参数字典，用于存储所有配置
    params = {}
    
    # 基础参数
    st.sidebar.subheader("💰 基础参数")
    
    # 初始资金
    initial_capital_config = StrategyConfig.get_streamlit_config('initial_capital')
    params['initial_capital'] = st.sidebar.number_input(
        "初始资金",
        **initial_capital_config
    )
    
    # 手续费率
    commission_config = StrategyConfig.get_streamlit_config('commission_rate')
    params['commission_rate'] = st.sidebar.slider(
        "手续费率",
        **commission_config,
        format="%.4f"
    )
    
    # 滑点
    slippage_config = StrategyConfig.get_streamlit_config('slippage')
    params['slippage'] = st.sidebar.slider(
        "滑点",
        **slippage_config,
        format="%.4f"
    )
    
    # 技术指标参数
    st.sidebar.subheader("📈 技术指标")
    
    # 短期均线
    ma_short_config = StrategyConfig.get_streamlit_config('ma_short')
    params['ma_short'] = st.sidebar.slider(
        "短期均线周期",
        **ma_short_config
    )
    
    # 长期均线
    ma_long_config = StrategyConfig.get_streamlit_config('ma_long')
    params['ma_long'] = st.sidebar.slider(
        "长期均线周期",
        **ma_long_config
    )
    
    # RSI参数
    rsi_period_config = StrategyConfig.get_streamlit_config('rsi_period')
    params['rsi_period'] = st.sidebar.slider(
        "RSI周期",
        **rsi_period_config
    )
    
    rsi_overbought_config = StrategyConfig.get_streamlit_config('rsi_overbought')
    params['rsi_overbought'] = st.sidebar.slider(
        "RSI超买阈值",
        **rsi_overbought_config
    )
    
    rsi_oversold_config = StrategyConfig.get_streamlit_config('rsi_oversold')
    params['rsi_oversold'] = st.sidebar.slider(
        "RSI超卖阈值",
        **rsi_oversold_config
    )
    
    # 套利参数
    st.sidebar.subheader("🎯 套利策略")
    
    # 价差阈值
    spread_config = StrategyConfig.get_streamlit_config('spread_threshold')
    params['spread_threshold'] = st.sidebar.slider(
        "价差阈值",
        **spread_config,
        format="%.3f"
    )
    
    # 仓位大小
    position_config = StrategyConfig.get_streamlit_config('position_size')
    params['position_size'] = st.sidebar.slider(
        "仓位大小",
        **position_config,
        format="%.1f"
    )
    
    # 止损比例
    stop_loss_config = StrategyConfig.get_streamlit_config('stop_loss')
    params['stop_loss'] = st.sidebar.slider(
        "止损比例",
        **stop_loss_config,
        format="%.2f"
    )
    
    # 止盈比例
    take_profit_config = StrategyConfig.get_streamlit_config('take_profit')
    params['take_profit'] = st.sidebar.slider(
        "止盈比例",
        **take_profit_config,
        format="%.2f"
    )
    
    # 最大持仓天数
    max_holding_config = StrategyConfig.get_streamlit_config('max_holding_days')
    params['max_holding_days'] = st.sidebar.slider(
        "最大持仓天数",
        **max_holding_config
    )
    
    # 风险管理参数
    st.sidebar.subheader("⚠️ 风险管理")
    
    # 最大回撤限制
    max_drawdown_config = StrategyConfig.get_streamlit_config('max_drawdown')
    params['max_drawdown'] = st.sidebar.slider(
        "最大回撤限制",
        **max_drawdown_config,
        format="%.2f"
    )
    
    # VaR置信度
    var_confidence_config = StrategyConfig.get_streamlit_config('var_confidence')
    params['var_confidence'] = st.sidebar.slider(
        "VaR置信度",
        **var_confidence_config,
        format="%.2f"
    )
    
    # 风险计算回望天数
    lookback_config = StrategyConfig.get_streamlit_config('lookback_days')
    params['lookback_days'] = st.sidebar.slider(
        "风险计算回望天数",
        **lookback_config
    )
    
    # 如果选择了预设配置，应用预设参数
    if config_preset in STRATEGY_PRESETS:
        preset_params = STRATEGY_PRESETS[config_preset]['params']
        for key, value in preset_params.items():
            if key in params:
                params[key] = value
        
        st.sidebar.info(f"已应用 {STRATEGY_PRESETS[config_preset]['name']} 预设配置")
    
    # 参数验证
    validation_errors = []
    for param_name, value in params.items():
        if not StrategyConfig.validate_param_value(param_name, value):
            min_val, max_val = StrategyConfig.get_param_bounds(param_name)
            validation_errors.append(f"{param_name}: {value} 不在有效范围 [{min_val}, {max_val}] 内")
    
    if validation_errors:
        st.sidebar.error("参数验证失败:")
        for error in validation_errors:
            st.sidebar.error(f"• {error}")
    
    return params, len(validation_errors) == 0

def show_parameter_summary(params):
    """显示参数摘要"""
    st.subheader("📋 当前参数配置")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.write("**基础参数**")
        st.write(f"初始资金: {params['initial_capital']:,.0f}")
        st.write(f"手续费率: {params['commission_rate']:.4f}")
        st.write(f"滑点: {params['slippage']:.4f}")
    
    with col2:
        st.write("**技术指标**")
        st.write(f"短期均线: {params['ma_short']}")
        st.write(f"长期均线: {params['ma_long']}")
        st.write(f"RSI周期: {params['rsi_period']}")
        st.write(f"RSI超买: {params['rsi_overbought']}")
        st.write(f"RSI超卖: {params['rsi_oversold']}")
    
    with col3:
        st.write("**套利策略**")
        st.write(f"价差阈值: {params['spread_threshold']:.3f}")
        st.write(f"仓位大小: {params['position_size']:.1f}")
        st.write(f"止损比例: {params['stop_loss']:.2f}")
        st.write(f"止盈比例: {params['take_profit']:.2f}")
        st.write(f"最大持仓: {params['max_holding_days']}天")

def main():
    """主函数示例"""
    st.title("🔧 统一参数配置示例")
    
    st.markdown("""
    这个示例展示了如何使用统一的 `strategy_config.py` 配置文件来管理：
    - 回测界面参数
    - 实时监控界面参数
    - 参数边界值验证
    - 预设配置管理
    """)
    
    # 创建参数配置界面
    params, is_valid = create_unified_parameter_ui()
    
    # 显示参数摘要
    if is_valid:
        show_parameter_summary(params)
        
        # 显示配置的优势
        st.subheader("✅ 统一配置的优势")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            **一致性保证**
            - 回测和实时监控使用相同参数边界
            - 避免参数设置不一致的问题
            - 统一的参数验证逻辑
            """)
        
        with col2:
            st.markdown("""
            **维护便利**
            - 集中管理所有参数配置
            - 易于添加新参数或修改边界值
            - 支持预设配置快速切换
            """)
        
        # 显示如何在代码中使用
        st.subheader("💻 代码使用示例")
        
        st.code("""
# 在回测界面中使用
from strategy_config import StrategyConfig

# 获取参数配置
config = StrategyConfig.get_streamlit_config('spread_threshold')
spread_threshold = st.sidebar.slider("价差阈值", **config)

# 参数验证
if StrategyConfig.validate_param_value('spread_threshold', spread_threshold):
    # 参数有效，继续处理
    pass

# 在实时监控界面中使用相同配置
# 确保两个界面的参数边界完全一致
        """, language='python')
    
    else:
        st.error("⚠️ 参数配置存在错误，请检查侧边栏中的错误提示")

if __name__ == "__main__":
    main()