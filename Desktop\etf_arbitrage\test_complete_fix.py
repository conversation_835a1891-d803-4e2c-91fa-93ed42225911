#!/usr/bin/env python3
"""
完整测试参数传递修复
模拟完整的回测流程，验证参数是否正确传递
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import pandas as pd
from datetime import datetime, timedelta
from backtest_enhanced import BacktestConfig

def create_test_data():
    """创建测试数据"""
    # 创建简单的测试数据
    start_time = datetime(2025, 8, 26, 9, 30, 0)
    times = [start_time + timedelta(seconds=i*3) for i in range(100)]
    
    # 模拟价格数据：先下跌触发买入，然后上涨触发止盈
    prices = []
    base_price = 0.770
    
    for i in range(100):
        if i < 20:
            # 前20个tick下跌
            price = base_price - (i * 0.001)
        elif i < 50:
            # 中间30个tick继续小幅下跌
            price = base_price - 0.020 + (i-20) * 0.0001
        else:
            # 后50个tick上涨
            price = base_price - 0.017 + (i-50) * 0.0005
        prices.append(price)
    
    df = pd.DataFrame({
        'time': times,
        'price': prices,
        'volume': [100000] * 100
    })
    
    return df

def test_with_default_config():
    """使用默认配置测试"""
    print("=== 使用默认配置测试 ===")
    
    # 创建默认配置
    config = BacktestConfig(
        symbol="159740",
        start_date="2025-08-26",
        end_date="2025-08-26",
        buy_trigger_drop=-0.006,
        profit_target=0.0025,
        stop_loss=-0.015
    )
    
    print(f"配置参数:")
    print(f"  买入触发: {config.buy_trigger_drop:.3f}")
    print(f"  止盈目标: {config.profit_target:.4f}")
    print(f"  第一次止盈倍数: {config.partial_profit_multiplier1:.1f}")
    print(f"  第二次止盈倍数: {config.partial_profit_multiplier2:.1f}")
    print(f"  第三次止盈倍数: {config.partial_profit_multiplier3:.1f}")
    
    # 计算实际止盈阈值
    threshold1 = config.profit_target * config.partial_profit_multiplier1
    threshold2 = config.profit_target * config.partial_profit_multiplier2
    threshold3 = config.profit_target * config.partial_profit_multiplier3
    
    print(f"  实际止盈阈值:")
    print(f"    第一次: {threshold1:.4f} ({threshold1:.2%})")
    print(f"    第二次: {threshold2:.4f} ({threshold2:.2%})")
    print(f"    第三次: {threshold3:.4f} ({threshold3:.2%})")
    
    return config

def test_with_custom_config():
    """使用自定义配置测试（模拟用户设置）"""
    print("\n=== 使用自定义配置测试（用户设置）===")
    
    # 创建自定义配置（模拟用户在面板上的设置）
    config = BacktestConfig(
        symbol="159740",
        start_date="2025-08-26",
        end_date="2025-08-26",
        buy_trigger_drop=-0.006,
        profit_target=0.0025,
        stop_loss=-0.015,
        # 用户自定义的止盈参数
        partial_profit_multiplier1=1.0,  # 1倍
        partial_profit_multiplier2=1.0,  # 1倍
        partial_profit_multiplier3=2.0,  # 2倍
        partial_sell_ratio1=0.3,         # 30%
        partial_sell_ratio2=0.4,         # 40%
        partial_sell_ratio3=0.3          # 30%
    )
    
    print(f"配置参数:")
    print(f"  买入触发: {config.buy_trigger_drop:.3f}")
    print(f"  止盈目标: {config.profit_target:.4f}")
    print(f"  第一次止盈倍数: {config.partial_profit_multiplier1:.1f}")
    print(f"  第二次止盈倍数: {config.partial_profit_multiplier2:.1f}")
    print(f"  第三次止盈倍数: {config.partial_profit_multiplier3:.1f}")
    
    # 计算实际止盈阈值
    threshold1 = config.profit_target * config.partial_profit_multiplier1
    threshold2 = config.profit_target * config.partial_profit_multiplier2
    threshold3 = config.profit_target * config.partial_profit_multiplier3
    
    print(f"  实际止盈阈值:")
    print(f"    第一次: {threshold1:.4f} ({threshold1:.2%})")
    print(f"    第二次: {threshold2:.4f} ({threshold2:.2%})")
    print(f"    第三次: {threshold3:.4f} ({threshold3:.2%})")
    
    return config

def test_position_behavior(config):
    """测试持仓行为"""
    print(f"\n=== 测试持仓行为 ===")
    
    from strategy_engine_enhanced import Position
    
    position = Position()
    position.add_position(100000, 0.767, datetime.now())
    
    # 测试不同价格下的止盈行为
    test_prices = [0.769, 0.770, 0.772, 0.775]
    
    for price in test_prices:
        profit_rate = position.get_profit_rate(price)
        should_sell, level, ratio, reason = position.check_partial_sell(price, config)
        
        print(f"价格: {price:.3f}, 收益率: {profit_rate:.4f} ({profit_rate:.2%})")
        print(f"  是否卖出: {should_sell}, 级别: {level}, 比例: {ratio:.1%}, 原因: {reason}")
        
        if should_sell:
            # 模拟执行卖出
            sell_qty = int(position.total_quantity * ratio)
            position.reduce_position(sell_qty)
            print(f"  执行卖出: {sell_qty}股, 剩余: {position.total_quantity}股")
        print()

def compare_configs():
    """对比不同配置的差异"""
    print("\n=== 配置对比 ===")
    
    # 默认配置
    default_config = BacktestConfig()
    
    # 自定义配置
    custom_config = BacktestConfig(
        partial_profit_multiplier1=1.0,
        partial_profit_multiplier2=1.0,
        partial_profit_multiplier3=2.0
    )
    
    print("配置对比:")
    print(f"参数                  默认配置    自定义配置")
    print(f"第一次止盈倍数        {default_config.partial_profit_multiplier1:.1f}x        {custom_config.partial_profit_multiplier1:.1f}x")
    print(f"第二次止盈倍数        {default_config.partial_profit_multiplier2:.1f}x        {custom_config.partial_profit_multiplier2:.1f}x")
    print(f"第三次止盈倍数        {default_config.partial_profit_multiplier3:.1f}x        {custom_config.partial_profit_multiplier3:.1f}x")
    
    # 计算阈值差异
    profit_target = 0.0025
    default_threshold1 = profit_target * default_config.partial_profit_multiplier1
    custom_threshold1 = profit_target * custom_config.partial_profit_multiplier1
    
    print(f"\n第一次止盈阈值:")
    print(f"默认配置: {default_threshold1:.4f} ({default_threshold1:.2%})")
    print(f"自定义配置: {custom_threshold1:.4f} ({custom_threshold1:.2%})")
    print(f"差异: {abs(default_threshold1 - custom_threshold1):.4f}")

if __name__ == "__main__":
    # 测试默认配置
    default_config = test_with_default_config()
    
    # 测试自定义配置
    custom_config = test_with_custom_config()
    
    # 对比配置
    compare_configs()
    
    # 测试持仓行为
    print("\n" + "="*50)
    print("测试默认配置下的持仓行为:")
    test_position_behavior(default_config)
    
    print("\n" + "="*50)
    print("测试自定义配置下的持仓行为:")
    test_position_behavior(custom_config)
    
    print("\n✅ 修复验证完成！")
    print("现在回测程序会正确使用面板设置的参数，而不是默认配置。")
