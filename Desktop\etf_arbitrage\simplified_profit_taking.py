#!/usr/bin/env python3
"""
简化分批止盈逻辑示例
展示新的简化止盈方案
"""

from typing import Tuple
from dataclasses import dataclass

@dataclass
class SimplifiedProfitConfig:
    """简化的止盈配置"""
    # 止盈级别（收益率阈值）
    level1_threshold: float = 0.003   # 0.3%
    level2_threshold: float = 0.006   # 0.6%
    level3_threshold: float = 0.012   # 1.2%
    
    # 对应的卖出比例
    level1_sell_ratio: float = 0.3    # 30%
    level2_sell_ratio: float = 0.5    # 50%
    level3_sell_ratio: float = 0.7    # 70%

class SimplifiedProfitTaking:
    """简化的分批止盈管理器"""
    
    def __init__(self, config: SimplifiedProfitConfig = None):
        self.config = config or SimplifiedProfitConfig()
        self.current_level = 0  # 当前已达到的止盈级别
        
    def reset(self):
        """重置止盈状态"""
        self.current_level = 0
        
    def check_profit_taking(self, profit_rate: float) -> Tuple[bool, float, str]:
        """
        检查是否应该止盈
        
        Args:
            profit_rate: 当前收益率
            
        Returns:
            (是否卖出, 卖出比例, 原因)
        """
        # 定义止盈级别
        levels = [
            (self.config.level1_threshold, self.config.level1_sell_ratio, "第一次止盈"),
            (self.config.level2_threshold, self.config.level2_sell_ratio, "第二次止盈"),
            (self.config.level3_threshold, self.config.level3_sell_ratio, "第三次止盈")
        ]
        
        # 检查是否达到新的止盈级别
        for level_idx, (threshold, sell_ratio, reason) in enumerate(levels):
            if profit_rate >= threshold and self.current_level <= level_idx:
                self.current_level = level_idx + 1
                return True, sell_ratio, f"{reason} (收益率: {profit_rate:.2%})"
        
        return False, 0.0, ""
    
    def get_status(self) -> dict:
        """获取当前止盈状态"""
        return {
            'current_level': self.current_level,
            'next_threshold': self._get_next_threshold(),
            'completed_levels': self.current_level,
            'remaining_levels': 3 - self.current_level
        }
    
    def _get_next_threshold(self) -> float:
        """获取下一个止盈阈值"""
        thresholds = [
            self.config.level1_threshold,
            self.config.level2_threshold,
            self.config.level3_threshold
        ]
        
        if self.current_level < len(thresholds):
            return thresholds[self.current_level]
        return float('inf')  # 所有级别已完成

# 使用示例
def example_usage():
    """使用示例"""
    profit_manager = SimplifiedProfitTaking()
    
    # 模拟价格变化和止盈检查
    test_scenarios = [
        (0.002, "收益率0.2%，未达到止盈条件"),
        (0.004, "收益率0.4%，触发第一次止盈"),
        (0.005, "收益率0.5%，第一次止盈已触发，不重复"),
        (0.007, "收益率0.7%，触发第二次止盈"),
        (0.015, "收益率1.5%，触发第三次止盈")
    ]
    
    print("=== 简化分批止盈测试 ===")
    for profit_rate, description in test_scenarios:
        should_sell, sell_ratio, reason = profit_manager.check_profit_taking(profit_rate)
        status = profit_manager.get_status()
        
        print(f"\n{description}")
        print(f"收益率: {profit_rate:.2%}")
        print(f"是否卖出: {should_sell}")
        if should_sell:
            print(f"卖出比例: {sell_ratio:.1%}")
            print(f"卖出原因: {reason}")
        print(f"当前级别: {status['current_level']}/3")
        print(f"下一阈值: {status['next_threshold']:.2%}")

if __name__ == "__main__":
    example_usage()
